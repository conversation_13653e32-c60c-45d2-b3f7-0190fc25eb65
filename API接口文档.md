# 校园跑腿外卖管理系统 API 接口文档

## 概述

本文档描述了校园跑腿外卖管理系统的所有API接口，包括用户管理、订单管理、商家管理、商品管理、配送管理、财务管理等核心业务模块。

## 基础信息

- **Base URL**: `http://localhost:3000/api`
- **认证方式**: Bearer <PERSON>
- **数据格式**: JSON
- **字符编码**: UTF-8

## 通用响应格式

```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## 1. 用户管理模块

### 1.1 学生用户管理

#### 获取学生用户列表
- **接口**: `GET /students`
- **参数**:
  - `page`: 页码 (默认: 1)
  - `pageSize`: 每页数量 (默认: 20)
  - `name`: 姓名 (可选)
  - `phone`: 手机号 (可选)
  - `college`: 学院 (可选)
  - `status`: 状态 (可选)

#### 创建学生用户
- **接口**: `POST /students`
- **参数**:
```json
{
  "studentId": "2024001",
  "name": "张三",
  "phone": "13800138000",
  "college": "计算机学院",
  "major": "软件工程",
  "grade": "2024",
  "dormitory": "1号楼101",
  "status": "active"
}
```

#### 更新学生用户
- **接口**: `PATCH /students/{id}`

#### 删除学生用户
- **接口**: `DELETE /students/{id}`

#### 获取学生详情
- **接口**: `GET /students/{id}`

#### 重置学生密码
- **接口**: `POST /students/{id}/reset-password`

#### 获取学生统计
- **接口**: `GET /students/stats`

#### 获取学生消费记录
- **接口**: `GET /students/{id}/consumption`

#### 导出学生数据
- **接口**: `GET /students/export`

### 1.2 商家用户管理

#### 获取商家用户列表
- **接口**: `GET /merchants`
- **参数**:
  - `page`: 页码
  - `pageSize`: 每页数量
  - `name`: 商家名称 (可选)
  - `contactName`: 联系人 (可选)
  - `category`: 商家类型 (可选)
  - `businessStatus`: 营业状态 (可选)
  - `auditStatus`: 审核状态 (可选)

#### 创建商家用户
- **接口**: `POST /merchants`
- **参数**:
```json
{
  "name": "美味餐厅",
  "category": "food",
  "contactName": "李四",
  "contactPhone": "***********",
  "address": "校园内美食街1号",
  "description": "提供各种美味菜品",
  "businessHours": "09:00-22:00",
  "deliveryFee": 3.0,
  "minOrderAmount": 20.0,
  "businessStatus": "open"
}
```

#### 更新商家信息
- **接口**: `PATCH /merchants/{id}`

#### 删除商家
- **接口**: `DELETE /merchants/{id}`

#### 获取商家详情
- **接口**: `GET /merchants/{id}`

#### 审核商家
- **接口**: `PATCH /merchants/{id}/audit`

#### 切换营业状态
- **接口**: `PATCH /merchants/{id}/business-status`

#### 获取商家统计
- **接口**: `GET /merchants/{id}/stats`

#### 获取商家订单
- **接口**: `GET /merchants/{id}/orders`

#### 获取商家商品
- **接口**: `GET /merchants/{id}/products`

#### 获取商家收入
- **接口**: `GET /merchants/{id}/income`

#### 导出商家数据
- **接口**: `GET /merchants/export`

### 1.3 配送员管理

#### 获取配送员列表
- **接口**: `GET /delivery-users`
- **参数**:
  - `page`: 页码
  - `pageSize`: 每页数量
  - `name`: 姓名 (可选)
  - `phone`: 手机号 (可选)
  - `workStatus`: 工作状态 (可选)
  - `auditStatus`: 审核状态 (可选)

#### 创建配送员
- **接口**: `POST /delivery-users`
- **参数**:
```json
{
  "name": "王五",
  "phone": "***********",
  "idCard": "110101199001011234",
  "gender": "male",
  "address": "北京市朝阳区",
  "vehicleType": "electric",
  "vehicleNumber": "京A12345",
  "emergencyContact": "王六",
  "emergencyPhone": "***********",
  "workStatus": "offline",
  "auditStatus": "pending"
}
```

#### 更新配送员信息
- **接口**: `PATCH /delivery-users/{id}`

#### 删除配送员
- **接口**: `DELETE /delivery-users/{id}`

#### 获取配送员详情
- **接口**: `GET /delivery-users/{id}`

#### 审核配送员
- **接口**: `PATCH /delivery-users/{id}/audit`

#### 切换工作状态
- **接口**: `PATCH /delivery-users/{id}/work-status`

#### 获取配送员统计
- **接口**: `GET /delivery-users/{id}/stats`

#### 获取配送员订单
- **接口**: `GET /delivery-users/{id}/orders`

#### 获取配送员收入
- **接口**: `GET /delivery-users/{id}/income`

#### 获取配送员位置
- **接口**: `GET /delivery-users/{id}/location`

#### 导出配送员数据
- **接口**: `GET /delivery-users/export`

## 2. 订单管理模块

### 2.1 订单列表管理

#### 获取订单列表
- **接口**: `GET /orders`
- **参数**:
  - `page`: 页码
  - `pageSize`: 每页数量
  - `orderNo`: 订单号 (可选)
  - `userPhone`: 用户手机号 (可选)
  - `merchantName`: 商家名称 (可选)
  - `status`: 订单状态 (可选)
  - `paymentStatus`: 支付状态 (可选)
  - `dateRange`: 时间范围 (可选)

#### 获取订单详情
- **接口**: `GET /orders/{id}`

#### 更新订单状态
- **接口**: `PATCH /orders/{id}/status`

#### 分配配送员
- **接口**: `PATCH /orders/{id}/assign`

#### 取消订单
- **接口**: `PATCH /orders/{id}/cancel`

#### 退款订单
- **接口**: `PATCH /orders/{id}/refund`

#### 批量操作订单
- **接口**: `PATCH /orders/batch`

#### 导出订单数据
- **接口**: `GET /orders/export`

### 2.2 订单统计分析

#### 获取订单统计
- **接口**: `GET /orders/stats`

#### 获取订单趋势
- **接口**: `GET /orders/trends`

#### 获取日统计数据
- **接口**: `GET /orders/daily-stats`

#### 获取商家统计
- **接口**: `GET /orders/merchant-stats`

#### 获取配送员统计
- **接口**: `GET /orders/delivery-stats`

#### 获取用户统计
- **接口**: `GET /orders/user-stats`

#### 获取地区统计
- **接口**: `GET /orders/region-stats`

## 3. 商家管理模块

### 3.1 商家列表管理

#### 获取商家列表
- **接口**: `GET /merchant/list`

#### 获取商家详情
- **接口**: `GET /merchant/detail/{id}`

#### 更新商家状态
- **接口**: `PATCH /merchant/{id}/status`

#### 审核商家
- **接口**: `PATCH /merchant/{id}/audit`

#### 获取商家统计
- **接口**: `GET /merchant/{id}/stats`

#### 获取商家商品
- **接口**: `GET /merchant/{id}/products`

#### 获取商家订单
- **接口**: `GET /merchant/{id}/orders`

#### 导出商家数据
- **接口**: `GET /merchant/export`

### 3.2 商家审核管理

#### 获取待审核商家
- **接口**: `GET /merchant/audit/pending`

#### 获取审核详情
- **接口**: `GET /merchant/audit/detail/{id}`

#### 审核商家
- **接口**: `POST /merchant/audit/{id}`

#### 批量审核
- **接口**: `POST /merchant/audit/batch`

#### 获取审核历史
- **接口**: `GET /merchant/audit/history`

#### 获取审核统计
- **接口**: `GET /merchant/audit/stats`

## 4. 商品管理模块

### 4.1 商品分类管理

#### 获取分类列表
- **接口**: `GET /product/categories`

#### 创建分类
- **接口**: `POST /product/categories`

#### 更新分类
- **接口**: `PATCH /product/categories/{id}`

#### 删除分类
- **接口**: `DELETE /product/categories/{id}`

#### 获取分类详情
- **接口**: `GET /product/categories/{id}`

#### 更新分类状态
- **接口**: `PATCH /product/categories/{id}/status`

#### 获取分类树
- **接口**: `GET /product/categories/tree`

### 4.2 商品列表管理

#### 获取商品列表
- **接口**: `GET /products`

#### 获取商品详情
- **接口**: `GET /products/{id}`

#### 更新商品状态
- **接口**: `PATCH /products/{id}/status`

#### 获取商品统计
- **接口**: `GET /products/stats`

#### 批量操作商品
- **接口**: `PATCH /products/batch`

#### 导出商品数据
- **接口**: `GET /products/export`

## 5. 配送管理模块

### 5.1 配送任务管理

#### 获取配送任务
- **接口**: `GET /delivery/tasks`

#### 获取任务详情
- **接口**: `GET /delivery/tasks/{id}`

#### 获取可用配送员
- **接口**: `GET /delivery/tasks/{id}/available-users`

#### 分配配送员
- **接口**: `POST /delivery/tasks/{id}/assign`

#### 智能分配任务
- **接口**: `POST /delivery/tasks/auto-assign`

#### 更新任务状态
- **接口**: `PATCH /delivery/tasks/{id}/status`

#### 获取配送统计
- **接口**: `GET /delivery/stats`

## 6. 数据概览模块

### 6.1 仪表盘数据

#### 获取统计数据
- **接口**: `GET /dashboard/stats`

#### 获取最新订单
- **接口**: `GET /dashboard/recent-orders`

#### 获取热门商品
- **接口**: `GET /dashboard/hot-products`

#### 获取趋势数据
- **接口**: `GET /dashboard/trends`

#### 获取实时数据
- **接口**: `GET /dashboard/realtime`

## 7. 文件上传

### 7.1 图片上传

#### 上传图片
- **接口**: `POST /upload/image`
- **参数**: FormData (file)
- **响应**:
```json
{
  "code": 200,
  "data": {
    "url": "https://example.com/images/xxx.jpg"
  }
}
```

## 状态码说明

- `200`: 成功
- `400`: 请求参数错误
- `401`: 未授权
- `403`: 禁止访问
- `404`: 资源不存在
- `500`: 服务器内部错误

## 数据字典

### 用户状态
- `active`: 正常
- `inactive`: 禁用
- `suspended`: 暂停

### 订单状态
- `pending`: 待接单
- `accepted`: 已接单
- `preparing`: 制作中
- `delivering`: 配送中
- `completed`: 已完成
- `cancelled`: 已取消

### 支付状态
- `pending`: 待支付
- `paid`: 已支付
- `refunded`: 已退款

### 商家类型
- `food`: 餐饮美食
- `supermarket`: 生活超市
- `fruit`: 水果生鲜
- `drink`: 饮品奶茶
- `snack`: 零食小食
- `other`: 其他

### 营业状态
- `open`: 营业中
- `closed`: 休息中
- `suspended`: 暂停营业

### 审核状态
- `pending`: 待审核
- `approved`: 已通过
- `rejected`: 已拒绝

### 工作状态
- `online`: 在线
- `offline`: 离线
- `delivering`: 配送中
- `resting`: 休息中

### 商品状态
- `active`: 上架
- `inactive`: 下架
- `sold_out`: 售罄

### 配送工具
- `electric`: 电动车
- `bicycle`: 自行车
- `walking`: 步行

## 注意事项

1. 所有时间字段均为ISO 8601格式
2. 金额字段保留两位小数
3. 分页参数：page从1开始，pageSize默认20，最大100
4. 所有删除操作为软删除
5. 文件上传大小限制：图片最大5MB
6. API调用频率限制：每分钟最多1000次请求

## 更新日志

- v1.0.0 (2024-01-01): 初始版本，包含所有核心功能模块
