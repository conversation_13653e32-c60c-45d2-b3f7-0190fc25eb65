/**********************************
 * @Author: <PERSON>
 * @LastEditor: <PERSON>
 * @LastEditTime: 2023/12/13 20:54:36
 * @Email: <EMAIL>
 * Copyright © 2023 <PERSON>(大脸怪) | https://isme.top
 **********************************/

export const defaultLayout = 'normal'

export const defaultPrimaryColor = '#FF6B35'

// 控制 LayoutSetting 组件是否可见
export const layoutSettingVisible = true

export const naiveThemeOverrides = {
  common: {
    primaryColor: '#FF6B35FF',
    primaryColorHover: '#FF6B35E3',
    primaryColorPressed: '#E55A2BFF',
    primaryColorSuppl: '#FF6B35E3',
  },
}

export const basePermissions = [
  {
    code: 'Dashboard',
    name: '数据概览',
    type: 'MENU',
    path: '/dashboard',
    component: '/src/views/dashboard/index.vue',
    icon: 'i-material-symbols:dashboard',
    order: 1,
    enable: true,
    show: true,
  },
  {
    code: 'UserManagement',
    name: '用户管理',
    type: 'MENU',
    icon: 'i-material-symbols:group',
    order: 2,
    enable: true,
    show: true,
    children: [
      {
        code: 'StudentUsers',
        name: '学生用户',
        type: 'MENU',
        path: '/user/students',
        component: '/src/views/user/students/index.vue',
        icon: 'i-material-symbols:school',
        order: 1,
        enable: true,
        show: true,
      },
      {
        code: 'MerchantUsers',
        name: '商家用户',
        type: 'MENU',
        path: '/user/merchants',
        component: '/src/views/user/merchants/index.vue',
        icon: 'i-material-symbols:store',
        order: 2,
        enable: true,
        show: true,
      },
      {
        code: 'DeliveryUsers',
        name: '配送员',
        type: 'MENU',
        path: '/user/delivery',
        component: '/src/views/user/delivery/index.vue',
        icon: 'i-material-symbols:delivery-truck',
        order: 3,
        enable: true,
        show: true,
      },
    ],
  },
  {
    code: 'OrderManagement',
    name: '订单管理',
    type: 'MENU',
    icon: 'i-material-symbols:receipt-long',
    order: 3,
    enable: true,
    show: true,
    children: [
      {
        code: 'OrderList',
        name: '订单列表',
        type: 'MENU',
        path: '/order/list',
        component: '/src/views/order/list/index.vue',
        icon: 'i-material-symbols:list',
        order: 1,
        enable: true,
        show: true,
      },
      {
        code: 'OrderStatistics',
        name: '订单统计',
        type: 'MENU',
        path: '/order/statistics',
        component: '/src/views/order/statistics/index.vue',
        icon: 'i-material-symbols:analytics',
        order: 2,
        enable: true,
        show: true,
      },
    ],
  },
  {
    code: 'MerchantManagement',
    name: '商家管理',
    type: 'MENU',
    icon: 'i-material-symbols:storefront',
    order: 4,
    enable: true,
    show: true,
    children: [
      {
        code: 'MerchantList',
        name: '商家列表',
        type: 'MENU',
        path: '/merchant/list',
        component: '/src/views/merchant/list/index.vue',
        icon: 'i-material-symbols:store',
        order: 1,
        enable: true,
        show: true,
      },
      {
        code: 'MerchantAudit',
        name: '入驻审核',
        type: 'MENU',
        path: '/merchant/audit',
        component: '/src/views/merchant/audit/index.vue',
        icon: 'i-material-symbols:fact-check',
        order: 2,
        enable: true,
        show: true,
      },
    ],
  },
  {
    code: 'ProductManagement',
    name: '商品管理',
    type: 'MENU',
    icon: 'i-material-symbols:inventory',
    order: 5,
    enable: true,
    show: true,
    children: [
      {
        code: 'ProductCategory',
        name: '商品分类',
        type: 'MENU',
        path: '/product/category',
        component: '/src/views/product/category/index.vue',
        icon: 'i-material-symbols:category',
        order: 1,
        enable: true,
        show: true,
      },
      {
        code: 'ProductList',
        name: '商品列表',
        type: 'MENU',
        path: '/product/list',
        component: '/src/views/product/list/index.vue',
        icon: 'i-material-symbols:inventory-2',
        order: 2,
        enable: true,
        show: true,
      },
    ],
  },
  {
    code: 'DeliveryManagement',
    name: '配送管理',
    type: 'MENU',
    icon: 'i-material-symbols:local-shipping',
    order: 6,
    enable: true,
    show: true,
    children: [
      {
        code: 'DeliveryTask',
        name: '配送任务',
        type: 'MENU',
        path: '/delivery/task',
        component: '/src/views/delivery/task/index.vue',
        icon: 'i-material-symbols:task',
        order: 1,
        enable: true,
        show: true,
      },
      {
        code: 'DeliveryRoute',
        name: '配送路线',
        type: 'MENU',
        path: '/delivery/route',
        component: '/src/views/delivery/route/index.vue',
        icon: 'i-material-symbols:route',
        order: 2,
        enable: true,
        show: true,
      },
    ],
  },
  {
    code: 'FinanceManagement',
    name: '财务管理',
    type: 'MENU',
    icon: 'i-material-symbols:account-balance',
    order: 7,
    enable: true,
    show: true,
    children: [
      {
        code: 'IncomeStatistics',
        name: '收入统计',
        type: 'MENU',
        path: '/finance/income',
        component: '/src/views/finance/income/index.vue',
        icon: 'i-material-symbols:trending-up',
        order: 1,
        enable: true,
        show: true,
      },
      {
        code: 'Settlement',
        name: '结算管理',
        type: 'MENU',
        path: '/finance/settlement',
        component: '/src/views/finance/settlement/index.vue',
        icon: 'i-material-symbols:account-balance-wallet',
        order: 2,
        enable: true,
        show: true,
      },
    ],
  },
  {
    code: 'SystemSettings',
    name: '系统设置',
    type: 'MENU',
    icon: 'i-material-symbols:settings',
    order: 8,
    enable: true,
    show: true,
    children: [
      {
        code: 'DeliverySettings',
        name: '配送设置',
        type: 'MENU',
        path: '/system/delivery-settings',
        component: '/src/views/system/delivery-settings/index.vue',
        icon: 'i-material-symbols:local-shipping',
        order: 1,
        enable: true,
        show: true,
      },
      {
        code: 'Announcements',
        name: '公告管理',
        type: 'MENU',
        path: '/system/announcements',
        component: '/src/views/system/announcements/index.vue',
        icon: 'i-material-symbols:campaign',
        order: 2,
        enable: true,
        show: true,
      },
      {
        code: 'Feedback',
        name: '反馈管理',
        type: 'MENU',
        path: '/system/feedback',
        component: '/src/views/system/feedback/index.vue',
        icon: 'i-material-symbols:feedback',
        order: 3,
        enable: true,
        show: true,
      },
    ],
  },
]
