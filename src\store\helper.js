import { cloneDeep } from 'lodash-es'
import api from '@/api'
import { basePermissions } from '@/settings'

export async function getUserInfo() {
  try {
    const res = await api.getUser()
    const { id, username, profile, roles, currentRole } = res.data || {}
    return {
      id,
      username,
      avatar: profile?.avatar,
      nickName: profile?.nickName,
      gender: profile?.gender,
      address: profile?.address,
      email: profile?.email,
      roles,
      currentRole,
    }
  } catch (error) {
    console.warn('获取用户信息失败，使用默认用户信息:', error)
    // 开发环境下返回默认用户信息
    return {
      id: 1,
      username: 'admin',
      avatar: '',
      nickName: '管理员',
      gender: 'male',
      address: '校园管理中心',
      email: '<EMAIL>',
      roles: [{ id: 1, name: '超级管理员', code: 'admin' }],
      currentRole: { id: 1, name: '超级管理员', code: 'admin' },
    }
  }
}

export async function getPermissions() {
  let asyncPermissions = []
  try {
    const res = await api.getRolePermissions()
    asyncPermissions = res?.data || []
  }
  catch (error) {
    console.warn('获取权限数据失败，使用默认权限配置:', error)
    // 开发环境下直接使用基础权限配置
  }
  return cloneDeep(basePermissions).concat(asyncPermissions)
}
