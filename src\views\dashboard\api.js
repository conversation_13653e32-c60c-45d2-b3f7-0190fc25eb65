/**********************************
 * @Description: 数据概览API
 * @Author: Campus Delivery Admin
 * @Date: 2024-01-01
 **********************************/

import { request } from '@/utils'

export default {
  // 获取仪表盘统计数据
  getDashboardStats: (params = {}) => request.get('/dashboard/stats', { params }),
  
  // 获取最新订单
  getRecentOrders: () => request.get('/dashboard/recent-orders'),
  
  // 获取热门商品
  getHotProducts: () => request.get('/dashboard/hot-products'),
  
  // 获取趋势数据
  getTrendData: (params = {}) => request.get('/dashboard/trends', { params }),
  
  // 获取实时数据
  getRealTimeData: () => request.get('/dashboard/realtime'),
}
