<!--------------------------------
 - @Description: 数据概览
 - @Author: Campus Delivery Admin
 - @Date: 2024-01-01
 --------------------------------->

<template>
  <CommonPage show-footer>
    <template #action>
      <div class="flex-y-center justify-between">
        <h1 class="flex-y-center">
          <i class="i-material-symbols:dashboard mr-8 text-20" />
          数据概览
        </h1>
        <div class="flex-y-center gap-12">
          <n-date-picker
            v-model:value="dateRange"
            type="daterange"
            clearable
            placeholder="选择时间范围"
            @update:value="handleDateChange"
          />
          <NButton type="primary" @click="handleRefresh">
            <i class="i-material-symbols:refresh mr-4 text-14" />
            刷新数据
          </NButton>
        </div>
      </div>
    </template>

    <div class="space-y-16">
      <!-- 核心指标 -->
      <n-grid :cols="4" :x-gap="16">
        <n-grid-item>
          <n-card>
            <n-statistic label="今日订单" :value="stats.todayOrders">
              <template #prefix>
                <i class="i-material-symbols:receipt-long text-primary" />
              </template>
            </n-statistic>
          </n-card>
        </n-grid-item>
        <n-grid-item>
          <n-card>
            <n-statistic label="今日收入" :value="stats.todayIncome" :precision="2">
              <template #prefix>
                <span class="text-success">¥</span>
              </template>
            </n-statistic>
          </n-card>
        </n-grid-item>
        <n-grid-item>
          <n-card>
            <n-statistic label="活跃用户" :value="stats.activeUsers">
              <template #prefix>
                <i class="i-material-symbols:person text-info" />
              </template>
            </n-statistic>
          </n-card>
        </n-grid-item>
        <n-grid-item>
          <n-card>
            <n-statistic label="在线配送员" :value="stats.onlineDeliveryUsers">
              <template #prefix>
                <i class="i-material-symbols:delivery-truck text-warning" />
              </template>
            </n-statistic>
          </n-card>
        </n-grid-item>
      </n-grid>

      <!-- 业务概况 -->
      <n-grid :cols="3" :x-gap="16">
        <n-grid-item>
          <n-card title="用户统计">
            <n-grid :cols="2" :x-gap="16">
              <n-grid-item>
                <n-statistic label="学生用户" :value="stats.studentCount" />
              </n-grid-item>
              <n-grid-item>
                <n-statistic label="商家数量" :value="stats.merchantCount" />
              </n-grid-item>
              <n-grid-item>
                <n-statistic label="配送员" :value="stats.deliveryUserCount" />
              </n-grid-item>
              <n-grid-item>
                <n-statistic label="新增用户" :value="stats.newUsersToday" />
              </n-grid-item>
            </n-grid>
          </n-card>
        </n-grid-item>

        <n-grid-item>
          <n-card title="订单概况">
            <n-grid :cols="2" :x-gap="16">
              <n-grid-item>
                <n-statistic label="待处理" :value="stats.pendingOrders" />
              </n-grid-item>
              <n-grid-item>
                <n-statistic label="配送中" :value="stats.deliveringOrders" />
              </n-grid-item>
              <n-grid-item>
                <n-statistic label="已完成" :value="stats.completedOrders" />
              </n-grid-item>
              <n-grid-item>
                <n-statistic label="完成率" :value="stats.completionRate" suffix="%" />
              </n-grid-item>
            </n-grid>
          </n-card>
        </n-grid-item>

        <n-grid-item>
          <n-card title="商家概况">
            <n-grid :cols="2" :x-gap="16">
              <n-grid-item>
                <n-statistic label="营业中" :value="stats.openMerchants" />
              </n-grid-item>
              <n-grid-item>
                <n-statistic label="待审核" :value="stats.pendingMerchants" />
              </n-grid-item>
              <n-grid-item>
                <n-statistic label="商品总数" :value="stats.totalProducts" />
              </n-grid-item>
              <n-grid-item>
                <n-statistic label="热门商家" :value="stats.hotMerchants" />
              </n-grid-item>
            </n-grid>
          </n-card>
        </n-grid-item>
      </n-grid>

      <!-- 图表区域 -->
      <n-grid :cols="2" :x-gap="16">
        <n-grid-item>
          <n-card title="订单趋势">
            <div ref="orderTrendChart" class="h-300"></div>
          </n-card>
        </n-grid-item>

        <n-grid-item>
          <n-card title="收入趋势">
            <div ref="incomeTrendChart" class="h-300"></div>
          </n-card>
        </n-grid-item>
      </n-grid>

      <!-- 实时数据 -->
      <n-grid :cols="2" :x-gap="16">
        <n-grid-item>
          <n-card title="最新订单">
            <n-table :bordered="false" :single-line="false" max-height="300">
              <thead>
                <tr>
                  <th>订单号</th>
                  <th>用户</th>
                  <th>商家</th>
                  <th>金额</th>
                  <th>状态</th>
                  <th>时间</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="order in recentOrders" :key="order.id">
                  <td>{{ order.orderNo }}</td>
                  <td>{{ order.userName }}</td>
                  <td>{{ order.merchantName }}</td>
                  <td>¥{{ order.totalAmount }}</td>
                  <td>
                    <NTag :type="getOrderStatusType(order.status)" size="small">
                      {{ getOrderStatusText(order.status) }}
                    </NTag>
                  </td>
                  <td>{{ formatDateTime(order.createdAt) }}</td>
                </tr>
              </tbody>
            </n-table>
          </n-card>
        </n-grid-item>

        <n-grid-item>
          <n-card title="热门商品">
            <n-table :bordered="false" :single-line="false" max-height="300">
              <thead>
                <tr>
                  <th>商品名称</th>
                  <th>商家</th>
                  <th>销量</th>
                  <th>价格</th>
                  <th>评分</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="product in hotProducts" :key="product.id">
                  <td>{{ product.name }}</td>
                  <td>{{ product.merchantName }}</td>
                  <td>{{ product.salesCount }}</td>
                  <td>¥{{ product.price }}</td>
                  <td>{{ product.rating || 0 }}</td>
                </tr>
              </tbody>
            </n-table>
          </n-card>
        </n-grid-item>
      </n-grid>
    </div>
  </CommonPage>
</template>

<script setup>
import { NButton, NCard, NStatistic, NGrid, NGridItem, NTable, NTag } from 'naive-ui'
import * as echarts from 'echarts'
import { formatDateTime } from '@/utils'
import api from './api'

defineOptions({ name: 'Dashboard' })

const dateRange = ref([])
const stats = ref({
  todayOrders: 0,
  todayIncome: 0,
  activeUsers: 0,
  onlineDeliveryUsers: 0,
  studentCount: 0,
  merchantCount: 0,
  deliveryUserCount: 0,
  newUsersToday: 0,
  pendingOrders: 0,
  deliveringOrders: 0,
  completedOrders: 0,
  completionRate: 0,
  openMerchants: 0,
  pendingMerchants: 0,
  totalProducts: 0,
  hotMerchants: 0,
})

const recentOrders = ref([])
const hotProducts = ref([])

// 图表引用
const orderTrendChart = ref(null)
const incomeTrendChart = ref(null)

// 图表实例
let orderTrendChartInstance = null
let incomeTrendChartInstance = null

onMounted(() => {
  initCharts()
  loadData()
})

onUnmounted(() => {
  orderTrendChartInstance?.dispose()
  incomeTrendChartInstance?.dispose()
})

// 初始化图表
function initCharts() {
  nextTick(() => {
    orderTrendChartInstance = echarts.init(orderTrendChart.value)
    incomeTrendChartInstance = echarts.init(incomeTrendChart.value)
  })
}

// 加载数据
async function loadData() {
  try {
    const params = {}
    if (dateRange.value && dateRange.value.length === 2) {
      params.startDate = new Date(dateRange.value[0]).toISOString().split('T')[0]
      params.endDate = new Date(dateRange.value[1]).toISOString().split('T')[0]
    }

    const [statsRes, ordersRes, productsRes, trendsRes] = await Promise.all([
      api.getDashboardStats(params),
      api.getRecentOrders(),
      api.getHotProducts(),
      api.getTrendData(params),
    ])

    stats.value = statsRes.data
    recentOrders.value = ordersRes.data
    hotProducts.value = productsRes.data

    // 更新图表
    updateOrderTrendChart(trendsRes.data.orderTrends)
    updateIncomeTrendChart(trendsRes.data.incomeTrends)
  } catch (error) {
    $message.error('加载数据失败')
  }
}

// 更新订单趋势图
function updateOrderTrendChart(data) {
  const option = {
    tooltip: { trigger: 'axis' },
    xAxis: { type: 'category', data: data.map(item => item.date) },
    yAxis: { type: 'value' },
    series: [{
      type: 'line',
      data: data.map(item => item.count),
      smooth: true,
      itemStyle: { color: '#FF6B35' },
    }],
  }
  orderTrendChartInstance?.setOption(option)
}

// 更新收入趋势图
function updateIncomeTrendChart(data) {
  const option = {
    tooltip: { trigger: 'axis' },
    xAxis: { type: 'category', data: data.map(item => item.date) },
    yAxis: { type: 'value' },
    series: [{
      type: 'bar',
      data: data.map(item => item.amount),
      itemStyle: { color: '#36CFC9' },
    }],
  }
  incomeTrendChartInstance?.setOption(option)
}

// 获取订单状态类型
function getOrderStatusType(status) {
  const typeMap = {
    pending: 'warning',
    accepted: 'info',
    preparing: 'info',
    delivering: 'primary',
    completed: 'success',
    cancelled: 'error',
  }
  return typeMap[status] || 'default'
}

// 获取订单状态文本
function getOrderStatusText(status) {
  const textMap = {
    pending: '待接单',
    accepted: '已接单',
    preparing: '制作中',
    delivering: '配送中',
    completed: '已完成',
    cancelled: '已取消',
  }
  return textMap[status] || status
}

// 日期范围变化
function handleDateChange() {
  loadData()
}

// 刷新数据
function handleRefresh() {
  loadData()
  $message.success('数据已刷新')
}
</script>
