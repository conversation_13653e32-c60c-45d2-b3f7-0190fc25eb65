/**********************************
 * @Description: 配送任务管理API
 * @Author: Campus Delivery Admin
 * @Date: 2024-01-01
 **********************************/

import { request } from '@/utils'

export default {
  // 获取配送任务列表
  getDeliveryTasks: (params = {}) => request.get('/delivery/tasks', { params }),
  
  // 获取任务详情
  getTaskDetail: id => request.get(`/delivery/tasks/${id}`),
  
  // 获取配送员列表
  getDeliveryUsers: () => request.get('/delivery-users/simple'),
  
  // 获取可用配送员
  getAvailableDeliveryUsers: taskId => request.get(`/delivery/tasks/${taskId}/available-users`),
  
  // 分配配送员
  assignDeliveryUser: (taskId, data) => request.post(`/delivery/tasks/${taskId}/assign`, data),
  
  // 智能分配任务
  autoAssignTasks: () => request.post('/delivery/tasks/auto-assign'),
  
  // 更新任务状态
  updateTaskStatus: (taskId, status) => request.patch(`/delivery/tasks/${taskId}/status`, { status }),
  
  // 获取配送统计
  getDeliveryStats: (params = {}) => request.get('/delivery/stats', { params }),
}
