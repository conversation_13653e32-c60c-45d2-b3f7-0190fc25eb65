<!--------------------------------
 - @Description: 配送任务管理
 - @Author: Campus Delivery Admin
 - @Date: 2024-01-01
 --------------------------------->

<template>
  <CommonPage show-footer>
    <template #action>
      <div class="flex-y-center justify-between">
        <h1 class="flex-y-center">
          <i class="i-material-symbols:local-shipping mr-8 text-20" />
          配送任务管理
        </h1>
        <div class="flex-y-center gap-12">
          <NButton type="primary" @click="handleAutoAssign">
            <i class="i-material-symbols:auto-mode mr-4 text-14" />
            智能分配
          </NButton>
          <NButton @click="handleRefresh">
            <i class="i-material-symbols:refresh mr-4 text-14" />
            刷新数据
          </NButton>
        </div>
      </div>
    </template>

    <MeCrud
      ref="$table"
      v-model:query-items="queryItems"
      :scroll-x="1800"
      :columns="columns"
      :get-data="api.getDeliveryTasks"
    >
      <MeQueryItem label="订单号" :label-width="60">
        <n-input
          v-model:value="queryItems.orderNo"
          type="text"
          placeholder="请输入订单号"
          clearable
        />
      </MeQueryItem>

      <MeQueryItem label="配送员" :label-width="60">
        <n-select
          v-model:value="queryItems.deliveryUserId"
          clearable
          placeholder="请选择配送员"
          :options="deliveryUserOptions"
          remote
          :loading="deliveryUserLoading"
          @focus="loadDeliveryUsers"
        />
      </MeQueryItem>

      <MeQueryItem label="任务状态" :label-width="70">
        <n-select
          v-model:value="queryItems.status"
          clearable
          :options="statusOptions"
        />
      </MeQueryItem>

      <MeQueryItem label="配送时间" :label-width="70">
        <n-date-picker
          v-model:value="queryItems.dateRange"
          type="daterange"
          clearable
          placeholder="选择时间范围"
        />
      </MeQueryItem>
    </MeCrud>

    <!-- 任务详情弹窗 -->
    <MeModal ref="detailModalRef" width="900px" title="配送任务详情">
      <div v-if="taskDetail" class="space-y-16">
        <!-- 基本信息 -->
        <n-card title="基本信息" size="small">
          <n-descriptions :column="3" bordered>
            <n-descriptions-item label="订单号">{{ taskDetail.orderNo }}</n-descriptions-item>
            <n-descriptions-item label="任务状态">
              <NTag :type="getStatusType(taskDetail.status)">
                {{ getStatusText(taskDetail.status) }}
              </NTag>
            </n-descriptions-item>
            <n-descriptions-item label="配送费">¥{{ taskDetail.deliveryFee }}</n-descriptions-item>
            <n-descriptions-item label="创建时间">{{ formatDateTime(taskDetail.createdAt) }}</n-descriptions-item>
            <n-descriptions-item label="接单时间">{{ formatDateTime(taskDetail.acceptedAt) }}</n-descriptions-item>
            <n-descriptions-item label="完成时间">{{ formatDateTime(taskDetail.completedAt) }}</n-descriptions-item>
          </n-descriptions>
        </n-card>

        <!-- 配送员信息 -->
        <n-card v-if="taskDetail.deliveryUser" title="配送员信息" size="small">
          <n-descriptions :column="2" bordered>
            <n-descriptions-item label="姓名">{{ taskDetail.deliveryUser.name }}</n-descriptions-item>
            <n-descriptions-item label="电话">{{ taskDetail.deliveryUser.phone }}</n-descriptions-item>
            <n-descriptions-item label="配送工具">{{ taskDetail.deliveryUser.vehicleType }}</n-descriptions-item>
            <n-descriptions-item label="车牌号">{{ taskDetail.deliveryUser.vehicleNumber }}</n-descriptions-item>
          </n-descriptions>
        </n-card>

        <!-- 地址信息 -->
        <n-card title="地址信息" size="small">
          <n-descriptions :column="1" bordered>
            <n-descriptions-item label="取货地址">{{ taskDetail.pickupAddress }}</n-descriptions-item>
            <n-descriptions-item label="送货地址">{{ taskDetail.deliveryAddress }}</n-descriptions-item>
            <n-descriptions-item label="预计距离">{{ taskDetail.estimatedDistance }}km</n-descriptions-item>
            <n-descriptions-item label="预计时间">{{ taskDetail.estimatedTime }}分钟</n-descriptions-item>
          </n-descriptions>
        </n-card>

        <!-- 配送轨迹 -->
        <n-card v-if="taskDetail.tracks?.length" title="配送轨迹" size="small">
          <n-timeline>
            <n-timeline-item
              v-for="track in taskDetail.tracks"
              :key="track.id"
              :type="getTrackType(track.status)"
              :title="track.description"
              :time="formatDateTime(track.createdAt)"
            />
          </n-timeline>
        </n-card>
      </div>
    </MeModal>

    <!-- 分配配送员弹窗 -->
    <MeModal ref="assignModalRef" width="600px" title="分配配送员">
      <n-form
        ref="assignFormRef"
        label-placement="left"
        label-align="left"
        :label-width="100"
        :model="assignForm"
      >
        <n-form-item
          label="选择配送员"
          path="deliveryUserId"
          :rule="{
            required: true,
            message: '请选择配送员',
            trigger: ['change', 'blur'],
          }"
        >
          <n-select
            v-model:value="assignForm.deliveryUserId"
            placeholder="请选择配送员"
            :options="availableDeliveryUsers"
            :render-label="renderDeliveryUserLabel"
          />
        </n-form-item>

        <n-form-item label="备注" path="remark">
          <n-input
            v-model:value="assignForm.remark"
            type="textarea"
            placeholder="分配备注（可选）"
            :rows="3"
          />
        </n-form-item>
      </n-form>
    </MeModal>
  </CommonPage>
</template>

<script setup>
import { NButton, NTag, NCard, NDescriptions, NDescriptionsItem, NTimeline, NTimelineItem } from 'naive-ui'
import { MeCrud, MeModal, MeQueryItem } from '@/components'
import { formatDateTime } from '@/utils'
import api from './api'

defineOptions({ name: 'DeliveryTask' })

const $table = ref(null)
const detailModalRef = ref(null)
const assignModalRef = ref(null)
const assignFormRef = ref(null)
const queryItems = ref({})
const taskDetail = ref(null)
const deliveryUserOptions = ref([])
const deliveryUserLoading = ref(false)
const availableDeliveryUsers = ref([])

const assignForm = ref({
  deliveryUserId: '',
  remark: '',
})

// 任务状态选项
const statusOptions = [
  { label: '待分配', value: 'pending' },
  { label: '已分配', value: 'assigned' },
  { label: '配送中', value: 'delivering' },
  { label: '已完成', value: 'completed' },
  { label: '已取消', value: 'cancelled' },
]

onMounted(() => {
  $table.value?.handleSearch()
  loadDeliveryUsers()
})

// 加载配送员选项
async function loadDeliveryUsers() {
  if (deliveryUserLoading.value) return
  deliveryUserLoading.value = true
  try {
    const { data } = await api.getDeliveryUsers()
    deliveryUserOptions.value = data.map(item => ({
      label: `${item.name} (${item.phone})`,
      value: item.id,
    }))
  } catch (error) {
    $message.error('加载配送员失败')
  } finally {
    deliveryUserLoading.value = false
  }
}

// 获取状态类型
function getStatusType(status) {
  const typeMap = {
    pending: 'warning',
    assigned: 'info',
    delivering: 'primary',
    completed: 'success',
    cancelled: 'error',
  }
  return typeMap[status] || 'default'
}

// 获取状态文本
function getStatusText(status) {
  const textMap = {
    pending: '待分配',
    assigned: '已分配',
    delivering: '配送中',
    completed: '已完成',
    cancelled: '已取消',
  }
  return textMap[status] || status
}

// 获取轨迹类型
function getTrackType(status) {
  const typeMap = {
    created: 'default',
    assigned: 'info',
    picked_up: 'warning',
    delivering: 'primary',
    completed: 'success',
    cancelled: 'error',
  }
  return typeMap[status] || 'default'
}

// 查看任务详情
async function handleViewDetail(row) {
  try {
    const { data } = await api.getTaskDetail(row.id)
    taskDetail.value = data
    detailModalRef.value?.open({
      title: `配送任务详情 - ${row.orderNo}`,
      showFooter: false,
    })
  } catch (error) {
    $message.error('获取任务详情失败')
  }
}

// 分配配送员
async function handleAssign(row) {
  try {
    const { data } = await api.getAvailableDeliveryUsers(row.id)
    availableDeliveryUsers.value = data.map(item => ({
      label: `${item.name} (${item.phone}) - 距离${item.distance}km`,
      value: item.id,
      ...item,
    }))
    
    assignForm.value = { deliveryUserId: '', remark: '' }
    assignModalRef.value?.open({
      title: `分配配送员 - ${row.orderNo}`,
      onOk: async () => {
        try {
          await assignFormRef.value?.validate()
          await api.assignDeliveryUser(row.id, assignForm.value)
          $message.success('分配成功')
          $table.value?.handleSearch()
          return true
        } catch (error) {
          if (error.message) {
            $message.error(error.message)
          }
          return false
        }
      },
    })
  } catch (error) {
    $message.error('获取可用配送员失败')
  }
}

// 渲染配送员标签
function renderDeliveryUserLabel(option) {
  return h('div', { class: 'flex justify-between' }, [
    h('span', option.label),
    h('span', { class: 'text-gray-500' }, `评分: ${option.rating || 0}`),
  ])
}

// 智能分配
async function handleAutoAssign() {
  try {
    await api.autoAssignTasks()
    $message.success('智能分配完成')
    $table.value?.handleSearch()
  } catch (error) {
    $message.error('智能分配失败')
  }
}

// 表格列配置
const columns = [
  { title: '订单号', key: 'orderNo', width: 180, fixed: 'left' },
  {
    title: '任务状态',
    key: 'status',
    width: 100,
    render: row => h(NTag, {
      type: getStatusType(row.status),
      size: 'small',
    }, { default: () => getStatusText(row.status) }),
  },
  { title: '配送员', key: 'deliveryUserName', width: 100 },
  { title: '取货地址', key: 'pickupAddress', width: 200 },
  { title: '送货地址', key: 'deliveryAddress', width: 200 },
  { title: '配送费', key: 'deliveryFee', width: 80, render: row => `¥${row.deliveryFee}` },
  { title: '预计距离', key: 'estimatedDistance', width: 100, render: row => `${row.estimatedDistance}km` },
  { title: '预计时间', key: 'estimatedTime', width: 100, render: row => `${row.estimatedTime}分钟` },
  { title: '创建时间', key: 'createdAt', width: 160, render: row => formatDateTime(row.createdAt) },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    fixed: 'right',
    render: row => [
      h(NButton, {
        size: 'small',
        type: 'primary',
        secondary: true,
        onClick: () => handleViewDetail(row),
      }, { default: () => '查看详情' }),
      ...(row.status === 'pending' ? [
        h(NButton, {
          size: 'small',
          type: 'warning',
          secondary: true,
          style: { marginLeft: '8px' },
          onClick: () => handleAssign(row),
        }, { default: () => '分配' }),
      ] : []),
    ],
  },
]

// 刷新数据
function handleRefresh() {
  $table.value?.handleSearch()
  $message.success('数据已刷新')
}
</script>
