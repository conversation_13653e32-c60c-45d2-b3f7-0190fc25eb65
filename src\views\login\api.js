/**********************************
 * @Author: <PERSON>
 * @LastEditor: <PERSON>
 * @LastEditTime: 2023/12/05 21:28:30
 * @Email: <EMAIL>
 * Copyright © 2023 Ronnie <PERSON>(大脸怪) | https://isme.top
 **********************************/

import { request } from '@/utils'

export default {
  toggleRole: data => request.post('/auth/role/toggle', data),
  login: async (data) => {
    try {
      return await request.post('/auth/login', data, { needToken: false })
    } catch (error) {
      console.warn('登录API调用失败，使用模拟登录:', error)
      // 开发环境下返回模拟登录数据
      return {
        data: {
          accessToken: 'mock-access-token-' + Date.now(),
          refreshToken: 'mock-refresh-token-' + Date.now(),
          expires: Date.now() + 24 * 60 * 60 * 1000, // 24小时后过期
        }
      }
    }
  },
  getUser: () => request.get('/user/detail'),
}
