/**********************************
 * @Description: 商家审核管理API
 * @Author: Campus Delivery Admin
 * @Date: 2024-01-01
 **********************************/

import { request } from '@/utils'

export default {
  // 获取待审核商家列表
  getPendingMerchants: (params = {}) => request.get('/merchant/audit/pending', { params }),
  
  // 获取商家审核详情
  getMerchantAuditDetail: id => request.get(`/merchant/audit/detail/${id}`),
  
  // 审核商家
  auditMerchant: (id, data) => request.post(`/merchant/audit/${id}`, data),
  
  // 批量审核商家
  batchAuditMerchants: data => request.post('/merchant/audit/batch', data),
  
  // 获取审核历史
  getAuditHistory: (params = {}) => request.get('/merchant/audit/history', { params }),
  
  // 获取审核统计
  getAuditStats: (params = {}) => request.get('/merchant/audit/stats', { params }),
}
