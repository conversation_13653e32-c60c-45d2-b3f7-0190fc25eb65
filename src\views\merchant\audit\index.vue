<!--------------------------------
 - @Description: 商家审核管理
 - @Author: Campus Delivery Admin
 - @Date: 2024-01-01
 --------------------------------->

<template>
  <CommonPage show-footer>
    <template #action>
      <div class="flex-y-center justify-between">
        <h1 class="flex-y-center">
          <i class="i-material-symbols:fact-check mr-8 text-20" />
          商家审核管理
        </h1>
        <div class="flex-y-center gap-12">
          <NButton type="primary" @click="handleBatchAudit">
            <i class="i-material-symbols:check-circle mr-4 text-14" />
            批量审核
          </NButton>
          <NButton @click="handleRefresh">
            <i class="i-material-symbols:refresh mr-4 text-14" />
            刷新数据
          </NButton>
        </div>
      </div>
    </template>

    <MeCrud
      ref="$table"
      v-model:query-items="queryItems"
      :scroll-x="1600"
      :columns="columns"
      :get-data="api.getPendingMerchants"
      row-key="id"
      @update:checked-row-keys="handleSelectionChange"
    >
      <MeQueryItem label="商家名称" :label-width="70">
        <n-input
          v-model:value="queryItems.name"
          type="text"
          placeholder="请输入商家名称"
          clearable
        />
      </MeQueryItem>

      <MeQueryItem label="商家类型" :label-width="70">
        <n-select
          v-model:value="queryItems.category"
          clearable
          placeholder="请选择商家类型"
          :options="categoryOptions"
        />
      </MeQueryItem>

      <MeQueryItem label="申请时间" :label-width="70">
        <n-date-picker
          v-model:value="queryItems.dateRange"
          type="daterange"
          clearable
          placeholder="选择时间范围"
        />
      </MeQueryItem>
    </MeCrud>

    <!-- 审核弹窗 -->
    <MeModal ref="auditModalRef" width="800px">
      <div v-if="auditData" class="space-y-16">
        <!-- 商家基本信息 -->
        <n-card title="商家基本信息" size="small">
          <n-descriptions :column="2" bordered>
            <n-descriptions-item label="商家名称">{{ auditData.name }}</n-descriptions-item>
            <n-descriptions-item label="商家类型">{{ getCategoryText(auditData.category) }}</n-descriptions-item>
            <n-descriptions-item label="联系人">{{ auditData.contactName }}</n-descriptions-item>
            <n-descriptions-item label="联系电话">{{ auditData.contactPhone }}</n-descriptions-item>
            <n-descriptions-item label="商家地址">{{ auditData.address }}</n-descriptions-item>
            <n-descriptions-item label="营业时间">{{ auditData.businessHours }}</n-descriptions-item>
            <n-descriptions-item label="配送费">¥{{ auditData.deliveryFee }}</n-descriptions-item>
            <n-descriptions-item label="起送金额">¥{{ auditData.minOrderAmount }}</n-descriptions-item>
          </n-descriptions>
        </n-card>

        <!-- 资质证件 -->
        <n-card title="资质证件" size="small">
          <n-grid :cols="3" :x-gap="16" :y-gap="16">
            <n-grid-item v-for="cert in auditData.certificates" :key="cert.type">
              <div class="text-center">
                <div class="mb-8 font-medium">{{ cert.name }}</div>
                <n-image
                  :src="cert.url"
                  width="150"
                  height="100"
                  object-fit="cover"
                  preview-disabled
                  @click="handlePreviewImage(cert.url)"
                />
              </div>
            </n-grid-item>
          </n-grid>
        </n-card>

        <!-- 审核表单 -->
        <n-card title="审核操作" size="small">
          <n-form
            ref="auditFormRef"
            label-placement="left"
            label-align="left"
            :label-width="100"
            :model="auditForm"
          >
            <n-form-item
              label="审核结果"
              path="result"
              :rule="{
                required: true,
                message: '请选择审核结果',
                trigger: ['change', 'blur'],
              }"
            >
              <n-radio-group v-model:value="auditForm.result">
                <n-radio value="approved">通过</n-radio>
                <n-radio value="rejected">拒绝</n-radio>
              </n-radio-group>
            </n-form-item>

            <n-form-item
              v-if="auditForm.result === 'rejected'"
              label="拒绝原因"
              path="reason"
              :rule="{
                required: auditForm.result === 'rejected',
                message: '请输入拒绝原因',
                trigger: ['input', 'blur'],
              }"
            >
              <n-input
                v-model:value="auditForm.reason"
                type="textarea"
                placeholder="请详细说明拒绝原因"
                :rows="3"
              />
            </n-form-item>

            <n-form-item label="审核备注" path="remark">
              <n-input
                v-model:value="auditForm.remark"
                type="textarea"
                placeholder="审核备注（可选）"
                :rows="2"
              />
            </n-form-item>
          </n-form>
        </n-card>
      </div>
    </MeModal>

    <!-- 批量审核弹窗 -->
    <MeModal ref="batchAuditModalRef" width="500px" title="批量审核">
      <n-form
        ref="batchAuditFormRef"
        label-placement="left"
        label-align="left"
        :label-width="100"
        :model="batchAuditForm"
      >
        <n-form-item
          label="审核结果"
          path="result"
          :rule="{
            required: true,
            message: '请选择审核结果',
            trigger: ['change', 'blur'],
          }"
        >
          <n-radio-group v-model:value="batchAuditForm.result">
            <n-radio value="approved">批量通过</n-radio>
            <n-radio value="rejected">批量拒绝</n-radio>
          </n-radio-group>
        </n-form-item>

        <n-form-item
          v-if="batchAuditForm.result === 'rejected'"
          label="拒绝原因"
          path="reason"
          :rule="{
            required: batchAuditForm.result === 'rejected',
            message: '请输入拒绝原因',
            trigger: ['input', 'blur'],
          }"
        >
          <n-input
            v-model:value="batchAuditForm.reason"
            type="textarea"
            placeholder="请详细说明拒绝原因"
            :rows="3"
          />
        </n-form-item>

        <n-form-item label="审核备注" path="remark">
          <n-input
            v-model:value="batchAuditForm.remark"
            type="textarea"
            placeholder="审核备注（可选）"
            :rows="2"
          />
        </n-form-item>
      </n-form>
    </MeModal>

    <!-- 图片预览弹窗 -->
    <MeModal ref="imagePreviewModalRef" width="600px" title="图片预览">
      <div class="text-center">
        <n-image :src="previewImageUrl" width="500" />
      </div>
    </MeModal>
  </CommonPage>
</template>

<script setup>
import { NButton, NTag, NCard, NDescriptions, NDescriptionsItem, NImage, NGrid, NGridItem, NRadioGroup, NRadio } from 'naive-ui'
import { MeCrud, MeModal, MeQueryItem } from '@/components'
import { formatDateTime } from '@/utils'
import api from './api'

defineOptions({ name: 'MerchantAudit' })

const $table = ref(null)
const auditModalRef = ref(null)
const batchAuditModalRef = ref(null)
const imagePreviewModalRef = ref(null)
const auditFormRef = ref(null)
const batchAuditFormRef = ref(null)

const queryItems = ref({})
const auditData = ref(null)
const selectedRowKeys = ref([])
const previewImageUrl = ref('')

const auditForm = ref({
  result: '',
  reason: '',
  remark: '',
})

const batchAuditForm = ref({
  result: '',
  reason: '',
  remark: '',
})

// 商家类型选项
const categoryOptions = [
  { label: '餐饮美食', value: 'food' },
  { label: '生活超市', value: 'supermarket' },
  { label: '水果生鲜', value: 'fruit' },
  { label: '饮品奶茶', value: 'drink' },
  { label: '零食小食', value: 'snack' },
  { label: '其他', value: 'other' },
]

onMounted(() => {
  $table.value?.handleSearch()
})

// 获取类型文本
function getCategoryText(category) {
  const option = categoryOptions.find(item => item.value === category)
  return option?.label || category
}

// 审核商家
async function handleAudit(row) {
  try {
    const { data } = await api.getMerchantAuditDetail(row.id)
    auditData.value = data
    auditForm.value = { result: '', reason: '', remark: '' }
    
    auditModalRef.value?.open({
      title: `审核商家 - ${row.name}`,
      onOk: async () => {
        try {
          await auditFormRef.value?.validate()
          await api.auditMerchant(row.id, auditForm.value)
          $message.success('审核成功')
          $table.value?.handleSearch()
          return true
        } catch (error) {
          if (error.message) {
            $message.error(error.message)
          }
          return false
        }
      },
    })
  } catch (error) {
    $message.error('获取审核详情失败')
  }
}

// 选择变化
function handleSelectionChange(keys) {
  selectedRowKeys.value = keys
}

// 批量审核
function handleBatchAudit() {
  if (selectedRowKeys.value.length === 0) {
    $message.warning('请先选择要审核的商家')
    return
  }

  batchAuditForm.value = { result: '', reason: '', remark: '' }
  batchAuditModalRef.value?.open({
    onOk: async () => {
      try {
        await batchAuditFormRef.value?.validate()
        await api.batchAuditMerchants({
          ids: selectedRowKeys.value,
          ...batchAuditForm.value,
        })
        $message.success('批量审核成功')
        selectedRowKeys.value = []
        $table.value?.handleSearch()
        return true
      } catch (error) {
        if (error.message) {
          $message.error(error.message)
        }
        return false
      }
    },
  })
}

// 预览图片
function handlePreviewImage(url) {
  previewImageUrl.value = url
  imagePreviewModalRef.value?.open({
    showFooter: false,
  })
}

// 表格列配置
const columns = [
  { type: 'selection' },
  {
    title: '商家信息',
    key: 'info',
    width: 200,
    render: row => h('div', { class: 'flex items-center gap-8' }, [
      h('img', {
        src: row.logo || '/default-store.png',
        class: 'w-40 h-40 rounded object-cover',
      }),
      h('div', [
        h('div', { class: 'font-medium' }, row.name),
        h('div', { class: 'text-12 text-gray-500' }, getCategoryText(row.category)),
      ]),
    ]),
  },
  { title: '联系人', key: 'contactName', width: 100 },
  { title: '联系电话', key: 'contactPhone', width: 120 },
  { title: '商家地址', key: 'address', width: 200 },
  { title: '配送费', key: 'deliveryFee', width: 80, render: row => `¥${row.deliveryFee}` },
  { title: '起送金额', key: 'minOrderAmount', width: 100, render: row => `¥${row.minOrderAmount}` },
  { title: '申请时间', key: 'createdAt', width: 160, render: row => formatDateTime(row.createdAt) },
  {
    title: '操作',
    key: 'actions',
    width: 120,
    fixed: 'right',
    render: row => [
      h(NButton, {
        size: 'small',
        type: 'primary',
        onClick: () => handleAudit(row),
      }, { default: () => '审核' }),
    ],
  },
]

// 刷新数据
function handleRefresh() {
  $table.value?.handleSearch()
  $message.success('数据已刷新')
}
</script>
