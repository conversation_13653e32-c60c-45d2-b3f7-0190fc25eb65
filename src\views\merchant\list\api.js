/**********************************
 * @Description: 商家列表管理API
 * @Author: Campus Delivery Admin
 * @Date: 2024-01-01
 **********************************/

import { request } from '@/utils'

export default {
  // 获取商家列表
  getMerchantList: (params = {}) => request.get('/merchant/list', { params }),
  
  // 获取商家详情
  getMerchantDetail: id => request.get(`/merchant/detail/${id}`),
  
  // 更新商家状态
  updateMerchantStatus: (id, data) => request.patch(`/merchant/${id}/status`, data),
  
  // 审核商家
  auditMerchant: (id, data) => request.patch(`/merchant/${id}/audit`, data),
  
  // 获取商家统计
  getMerchantStats: id => request.get(`/merchant/${id}/stats`),
  
  // 获取商家商品列表
  getMerchantProducts: (id, params = {}) => request.get(`/merchant/${id}/products`, { params }),
  
  // 获取商家订单列表
  getMerchantOrders: (id, params = {}) => request.get(`/merchant/${id}/orders`, { params }),
  
  // 导出商家数据
  exportMerchants: params => request.get('/merchant/export', { params, responseType: 'blob' }),
}
