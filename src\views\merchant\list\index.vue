<!--------------------------------
 - @Description: 商家列表管理
 - @Author: Campus Delivery Admin
 - @Date: 2024-01-01
 --------------------------------->

<template>
  <CommonPage show-footer>
    <template #action>
      <div class="flex-y-center justify-between">
        <h1 class="flex-y-center">
          <i class="i-material-symbols:store mr-8 text-20" />
          商家列表管理
        </h1>
        <div class="flex-y-center gap-12">
          <NButton @click="handleExport">
            <i class="i-material-symbols:download mr-4 text-14" />
            导出数据
          </NButton>
          <NButton type="primary" @click="handleRefresh">
            <i class="i-material-symbols:refresh mr-4 text-14" />
            刷新数据
          </NButton>
        </div>
      </div>
    </template>

    <MeCrud
      ref="$table"
      v-model:query-items="queryItems"
      :scroll-x="1800"
      :columns="columns"
      :get-data="api.getMerchantList"
    >
      <MeQueryItem label="商家名称" :label-width="70">
        <n-input
          v-model:value="queryItems.name"
          type="text"
          placeholder="请输入商家名称"
          clearable
        />
      </MeQueryItem>

      <MeQueryItem label="商家类型" :label-width="70">
        <n-select
          v-model:value="queryItems.category"
          clearable
          placeholder="请选择商家类型"
          :options="categoryOptions"
        />
      </MeQueryItem>

      <MeQueryItem label="营业状态" :label-width="70">
        <n-select
          v-model:value="queryItems.businessStatus"
          clearable
          :options="businessStatusOptions"
        />
      </MeQueryItem>

      <MeQueryItem label="审核状态" :label-width="70">
        <n-select
          v-model:value="queryItems.auditStatus"
          clearable
          :options="auditStatusOptions"
        />
      </MeQueryItem>
    </MeCrud>

    <!-- 商家详情弹窗 -->
    <MeModal ref="detailModalRef" width="1000px" title="商家详情">
      <div v-if="merchantDetail" class="space-y-16">
        <!-- 基本信息 -->
        <n-card title="基本信息" size="small">
          <n-descriptions :column="3" bordered>
            <n-descriptions-item label="商家名称">{{ merchantDetail.name }}</n-descriptions-item>
            <n-descriptions-item label="商家类型">{{ getCategoryText(merchantDetail.category) }}</n-descriptions-item>
            <n-descriptions-item label="联系人">{{ merchantDetail.contactName }}</n-descriptions-item>
            <n-descriptions-item label="联系电话">{{ merchantDetail.contactPhone }}</n-descriptions-item>
            <n-descriptions-item label="商家地址">{{ merchantDetail.address }}</n-descriptions-item>
            <n-descriptions-item label="营业时间">{{ merchantDetail.businessHours }}</n-descriptions-item>
            <n-descriptions-item label="配送费">¥{{ merchantDetail.deliveryFee }}</n-descriptions-item>
            <n-descriptions-item label="起送金额">¥{{ merchantDetail.minOrderAmount }}</n-descriptions-item>
            <n-descriptions-item label="评分">{{ merchantDetail.rating || 0 }}</n-descriptions-item>
          </n-descriptions>
        </n-card>

        <!-- 营业信息 -->
        <n-card title="营业信息" size="small">
          <n-descriptions :column="2" bordered>
            <n-descriptions-item label="营业状态">
              <NTag :type="getBusinessStatusType(merchantDetail.businessStatus)">
                {{ getBusinessStatusText(merchantDetail.businessStatus) }}
              </NTag>
            </n-descriptions-item>
            <n-descriptions-item label="审核状态">
              <NTag :type="getAuditStatusType(merchantDetail.auditStatus)">
                {{ getAuditStatusText(merchantDetail.auditStatus) }}
              </NTag>
            </n-descriptions-item>
            <n-descriptions-item label="入驻时间">{{ formatDateTime(merchantDetail.createdAt) }}</n-descriptions-item>
            <n-descriptions-item label="最后更新">{{ formatDateTime(merchantDetail.updatedAt) }}</n-descriptions-item>
          </n-descriptions>
        </n-card>

        <!-- 统计信息 -->
        <n-card title="统计信息" size="small">
          <n-grid :cols="4" :x-gap="16">
            <n-grid-item>
              <n-statistic label="总订单数" :value="merchantDetail.stats?.totalOrders || 0" />
            </n-grid-item>
            <n-grid-item>
              <n-statistic label="完成订单" :value="merchantDetail.stats?.completedOrders || 0" />
            </n-grid-item>
            <n-grid-item>
              <n-statistic label="总收入" :value="merchantDetail.stats?.totalIncome || 0" :precision="2">
                <template #prefix>¥</template>
              </n-statistic>
            </n-grid-item>
            <n-grid-item>
              <n-statistic label="商品数量" :value="merchantDetail.stats?.productCount || 0" />
            </n-grid-item>
          </n-grid>
        </n-card>

        <!-- 商品列表 -->
        <n-card title="商品列表" size="small">
          <n-table :bordered="false" :single-line="false" max-height="300">
            <thead>
              <tr>
                <th>商品名称</th>
                <th>分类</th>
                <th>价格</th>
                <th>库存</th>
                <th>状态</th>
                <th>销量</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="product in merchantDetail.products" :key="product.id">
                <td>{{ product.name }}</td>
                <td>{{ product.categoryName }}</td>
                <td>¥{{ product.price }}</td>
                <td>{{ product.stock }}</td>
                <td>
                  <NTag :type="product.status === 'active' ? 'success' : 'error'" size="small">
                    {{ product.status === 'active' ? '上架' : '下架' }}
                  </NTag>
                </td>
                <td>{{ product.salesCount }}</td>
              </tr>
            </tbody>
          </n-table>
        </n-card>
      </div>
    </MeModal>

    <!-- 状态操作弹窗 -->
    <MeModal ref="statusModalRef" width="500px">
      <n-form
        ref="statusFormRef"
        label-placement="left"
        label-align="left"
        :label-width="100"
        :model="statusForm"
      >
        <n-form-item label="操作类型" path="action">
          <n-select
            v-model:value="statusForm.action"
            :options="statusActionOptions"
            placeholder="请选择操作类型"
          />
        </n-form-item>
        <n-form-item v-if="statusForm.action === 'reject'" label="拒绝原因" path="reason">
          <n-input
            v-model:value="statusForm.reason"
            type="textarea"
            placeholder="请输入拒绝原因"
            :rows="3"
          />
        </n-form-item>
      </n-form>
    </MeModal>
  </CommonPage>
</template>

<script setup>
import { NButton, NTag, NCard, NDescriptions, NDescriptionsItem, NTable, NStatistic, NGrid, NGridItem } from 'naive-ui'
import { MeCrud, MeModal, MeQueryItem } from '@/components'
import { useCrud } from '@/composables'
import { formatDateTime } from '@/utils'
import api from './api'

defineOptions({ name: 'MerchantList' })

const $table = ref(null)
const detailModalRef = ref(null)
const statusModalRef = ref(null)
const statusFormRef = ref(null)
const queryItems = ref({})
const merchantDetail = ref(null)
const statusForm = ref({
  action: '',
  reason: '',
})

// 商家类型选项
const categoryOptions = [
  { label: '餐饮美食', value: 'food' },
  { label: '生活超市', value: 'supermarket' },
  { label: '水果生鲜', value: 'fruit' },
  { label: '饮品奶茶', value: 'drink' },
  { label: '零食小食', value: 'snack' },
  { label: '其他', value: 'other' },
]

// 营业状态选项
const businessStatusOptions = [
  { label: '营业中', value: 'open' },
  { label: '休息中', value: 'closed' },
  { label: '暂停营业', value: 'suspended' },
]

// 审核状态选项
const auditStatusOptions = [
  { label: '待审核', value: 'pending' },
  { label: '已通过', value: 'approved' },
  { label: '已拒绝', value: 'rejected' },
]

// 状态操作选项
const statusActionOptions = [
  { label: '通过审核', value: 'approve' },
  { label: '拒绝审核', value: 'reject' },
  { label: '暂停营业', value: 'suspend' },
  { label: '恢复营业', value: 'resume' },
]

onMounted(() => {
  $table.value?.handleSearch()
})

// 获取类型文本
function getCategoryText(category) {
  const option = categoryOptions.find(item => item.value === category)
  return option?.label || category
}

// 获取营业状态类型
function getBusinessStatusType(status) {
  const typeMap = {
    open: 'success',
    closed: 'warning',
    suspended: 'error',
  }
  return typeMap[status] || 'default'
}

// 获取营业状态文本
function getBusinessStatusText(status) {
  const option = businessStatusOptions.find(item => item.value === status)
  return option?.label || status
}

// 获取审核状态类型
function getAuditStatusType(status) {
  const typeMap = {
    pending: 'warning',
    approved: 'success',
    rejected: 'error',
  }
  return typeMap[status] || 'default'
}

// 获取审核状态文本
function getAuditStatusText(status) {
  const option = auditStatusOptions.find(item => item.value === status)
  return option?.label || status
}

// 查看商家详情
async function handleViewDetail(row) {
  try {
    const { data } = await api.getMerchantDetail(row.id)
    merchantDetail.value = data
    detailModalRef.value?.open({
      title: `商家详情 - ${row.name}`,
      showFooter: false,
    })
  } catch (error) {
    $message.error('获取商家详情失败')
  }
}

// 状态操作
function handleStatusAction(row) {
  statusForm.value = { action: '', reason: '' }
  statusModalRef.value?.open({
    title: `状态操作 - ${row.name}`,
    onOk: async () => {
      try {
        await api.updateMerchantStatus(row.id, statusForm.value)
        $message.success('操作成功')
        $table.value?.handleSearch()
        return true
      } catch (error) {
        $message.error('操作失败')
        return false
      }
    },
  })
}

// 表格列配置
const columns = [
  {
    title: '商家信息',
    key: 'info',
    width: 200,
    fixed: 'left',
    render: row => h('div', { class: 'flex items-center gap-8' }, [
      h('img', {
        src: row.logo || '/default-store.png',
        class: 'w-40 h-40 rounded object-cover',
      }),
      h('div', [
        h('div', { class: 'font-medium' }, row.name),
        h('div', { class: 'text-12 text-gray-500' }, getCategoryText(row.category)),
      ]),
    ]),
  },
  { title: '联系人', key: 'contactName', width: 100 },
  { title: '联系电话', key: 'contactPhone', width: 120 },
  {
    title: '营业状态',
    key: 'businessStatus',
    width: 100,
    render: row => h(NTag, {
      type: getBusinessStatusType(row.businessStatus),
      size: 'small',
    }, { default: () => getBusinessStatusText(row.businessStatus) }),
  },
  {
    title: '审核状态',
    key: 'auditStatus',
    width: 100,
    render: row => h(NTag, {
      type: getAuditStatusType(row.auditStatus),
      size: 'small',
    }, { default: () => getAuditStatusText(row.auditStatus) }),
  },
  { title: '评分', key: 'rating', width: 80, render: row => row.rating || 0 },
  { title: '订单数', key: 'orderCount', width: 80 },
  { title: '配送费', key: 'deliveryFee', width: 80, render: row => `¥${row.deliveryFee}` },
  { title: '入驻时间', key: 'createdAt', width: 160, render: row => formatDateTime(row.createdAt) },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    fixed: 'right',
    render: row => [
      h(NButton, {
        size: 'small',
        type: 'primary',
        secondary: true,
        onClick: () => handleViewDetail(row),
      }, { default: () => '查看详情' }),
      h(NButton, {
        size: 'small',
        type: 'warning',
        secondary: true,
        style: { marginLeft: '8px' },
        onClick: () => handleStatusAction(row),
      }, { default: () => '状态操作' }),
    ],
  },
]

// 刷新数据
function handleRefresh() {
  $table.value?.handleSearch()
  $message.success('数据已刷新')
}

// 导出数据
function handleExport() {
  $message.info('导出功能开发中...')
}
</script>
