/**********************************
 * @Description: 订单管理API
 * @Author: Campus Delivery Admin
 * @Date: 2024-01-01
 **********************************/

import { request } from '@/utils'

export default {
  // 获取订单列表
  getOrders: (params = {}) => request.get('/orders', { params }),
  
  // 获取订单详情
  getOrderDetail: id => request.get(`/orders/${id}`),
  
  // 更新订单状态
  updateOrderStatus: (id, status) => request.patch(`/orders/${id}/status`, { status }),
  
  // 分配配送员
  assignDeliveryUser: (id, deliveryUserId) => request.patch(`/orders/${id}/assign`, { deliveryUserId }),
  
  // 取消订单
  cancelOrder: (id, reason) => request.patch(`/orders/${id}/cancel`, { reason }),
  
  // 退款订单
  refundOrder: (id, amount, reason) => request.patch(`/orders/${id}/refund`, { amount, reason }),
  
  // 获取订单统计
  getOrderStats: (params = {}) => request.get('/orders/stats', { params }),
  
  // 获取订单趋势数据
  getOrderTrends: (params = {}) => request.get('/orders/trends', { params }),
  
  // 导出订单数据
  exportOrders: params => request.get('/orders/export', { params, responseType: 'blob' }),
  
  // 批量操作订单
  batchUpdateOrders: data => request.patch('/orders/batch', data),
}
