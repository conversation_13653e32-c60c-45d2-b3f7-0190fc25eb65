<!--------------------------------
 - @Description: 订单列表管理
 - @Author: Campus Delivery Admin
 - @Date: 2024-01-01
 --------------------------------->

<template>
  <CommonPage show-footer>
    <template #action>
      <div class="flex-y-center justify-between">
        <h1 class="flex-y-center">
          <i class="i-material-symbols:receipt-long mr-8 text-20" />
          订单管理
        </h1>
        <div class="flex-y-center gap-12">
          <NButton @click="handleExport">
            <i class="i-material-symbols:download mr-4 text-14" />
            导出订单
          </NButton>
          <NButton type="primary" @click="handleRefresh">
            <i class="i-material-symbols:refresh mr-4 text-14" />
            刷新数据
          </NButton>
        </div>
      </div>
    </template>

    <MeCrud
      ref="$table"
      v-model:query-items="queryItems"
      :scroll-x="1800"
      :columns="columns"
      :get-data="api.getOrders"
    >
      <MeQueryItem label="订单号" :label-width="60">
        <n-input
          v-model:value="queryItems.orderNo"
          type="text"
          placeholder="请输入订单号"
          clearable
        />
      </MeQueryItem>

      <MeQueryItem label="用户手机" :label-width="70">
        <n-input
          v-model:value="queryItems.userPhone"
          type="text"
          placeholder="请输入用户手机号"
          clearable
        />
      </MeQueryItem>

      <MeQueryItem label="商家名称" :label-width="70">
        <n-input
          v-model:value="queryItems.merchantName"
          type="text"
          placeholder="请输入商家名称"
          clearable
        />
      </MeQueryItem>

      <MeQueryItem label="订单状态" :label-width="70">
        <n-select
          v-model:value="queryItems.status"
          clearable
          placeholder="请选择订单状态"
          :options="statusOptions"
        />
      </MeQueryItem>

      <MeQueryItem label="支付状态" :label-width="70">
        <n-select
          v-model:value="queryItems.paymentStatus"
          clearable
          :options="[
            { label: '待支付', value: 'pending' },
            { label: '已支付', value: 'paid' },
            { label: '已退款', value: 'refunded' },
          ]"
        />
      </MeQueryItem>

      <MeQueryItem label="下单时间" :label-width="70">
        <n-date-picker
          v-model:value="queryItems.dateRange"
          type="daterange"
          clearable
          placeholder="选择时间范围"
        />
      </MeQueryItem>
    </MeCrud>

    <!-- 订单详情弹窗 -->
    <MeModal ref="detailModalRef" width="900px" title="订单详情">
      <div v-if="orderDetail" class="space-y-16">
        <!-- 基本信息 -->
        <n-card title="基本信息" size="small">
          <n-descriptions :column="3" bordered>
            <n-descriptions-item label="订单号">{{ orderDetail.orderNo }}</n-descriptions-item>
            <n-descriptions-item label="订单状态">
              <NTag :type="getStatusType(orderDetail.status)">
                {{ getStatusText(orderDetail.status) }}
              </NTag>
            </n-descriptions-item>
            <n-descriptions-item label="支付状态">
              <NTag :type="getPaymentStatusType(orderDetail.paymentStatus)">
                {{ getPaymentStatusText(orderDetail.paymentStatus) }}
              </NTag>
            </n-descriptions-item>
            <n-descriptions-item label="下单时间">{{ formatDateTime(orderDetail.createdAt) }}</n-descriptions-item>
            <n-descriptions-item label="订单金额">¥{{ orderDetail.totalAmount }}</n-descriptions-item>
            <n-descriptions-item label="配送费">¥{{ orderDetail.deliveryFee }}</n-descriptions-item>
          </n-descriptions>
        </n-card>

        <!-- 用户信息 -->
        <n-card title="用户信息" size="small">
          <n-descriptions :column="2" bordered>
            <n-descriptions-item label="用户姓名">{{ orderDetail.user?.name }}</n-descriptions-item>
            <n-descriptions-item label="联系电话">{{ orderDetail.user?.phone }}</n-descriptions-item>
            <n-descriptions-item label="收货地址">{{ orderDetail.deliveryAddress }}</n-descriptions-item>
            <n-descriptions-item label="备注">{{ orderDetail.remark || '无' }}</n-descriptions-item>
          </n-descriptions>
        </n-card>

        <!-- 商家信息 -->
        <n-card title="商家信息" size="small">
          <n-descriptions :column="2" bordered>
            <n-descriptions-item label="商家名称">{{ orderDetail.merchant?.name }}</n-descriptions-item>
            <n-descriptions-item label="商家电话">{{ orderDetail.merchant?.phone }}</n-descriptions-item>
            <n-descriptions-item label="商家地址">{{ orderDetail.merchant?.address }}</n-descriptions-item>
          </n-descriptions>
        </n-card>

        <!-- 商品信息 -->
        <n-card title="商品信息" size="small">
          <n-table :bordered="false" :single-line="false">
            <thead>
              <tr>
                <th>商品名称</th>
                <th>规格</th>
                <th>单价</th>
                <th>数量</th>
                <th>小计</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="item in orderDetail.items" :key="item.id">
                <td>{{ item.productName }}</td>
                <td>{{ item.specification || '-' }}</td>
                <td>¥{{ item.price }}</td>
                <td>{{ item.quantity }}</td>
                <td>¥{{ (item.price * item.quantity).toFixed(2) }}</td>
              </tr>
            </tbody>
          </n-table>
        </n-card>

        <!-- 配送信息 -->
        <n-card v-if="orderDetail.deliveryUser" title="配送信息" size="small">
          <n-descriptions :column="2" bordered>
            <n-descriptions-item label="配送员">{{ orderDetail.deliveryUser?.name }}</n-descriptions-item>
            <n-descriptions-item label="联系电话">{{ orderDetail.deliveryUser?.phone }}</n-descriptions-item>
            <n-descriptions-item label="接单时间">{{ formatDateTime(orderDetail.acceptedAt) }}</n-descriptions-item>
            <n-descriptions-item label="完成时间">{{ formatDateTime(orderDetail.completedAt) }}</n-descriptions-item>
          </n-descriptions>
        </n-card>
      </div>
    </MeModal>
  </CommonPage>
</template>

<script setup>
import { NButton, NTag, NCard, NDescriptions, NDescriptionsItem, NTable } from 'naive-ui'
import { MeCrud, MeModal, MeQueryItem } from '@/components'
import { formatDateTime } from '@/utils'
import api from './api'

defineOptions({ name: 'OrderList' })

const $table = ref(null)
const detailModalRef = ref(null)
const queryItems = ref({})
const orderDetail = ref(null)

// 订单状态选项
const statusOptions = [
  { label: '待接单', value: 'pending' },
  { label: '已接单', value: 'accepted' },
  { label: '制作中', value: 'preparing' },
  { label: '配送中', value: 'delivering' },
  { label: '已完成', value: 'completed' },
  { label: '已取消', value: 'cancelled' },
]

onMounted(() => {
  $table.value?.handleSearch()
})

// 获取状态类型
function getStatusType(status) {
  const typeMap = {
    pending: 'warning',
    accepted: 'info',
    preparing: 'info',
    delivering: 'primary',
    completed: 'success',
    cancelled: 'error',
  }
  return typeMap[status] || 'default'
}

// 获取状态文本
function getStatusText(status) {
  const textMap = {
    pending: '待接单',
    accepted: '已接单',
    preparing: '制作中',
    delivering: '配送中',
    completed: '已完成',
    cancelled: '已取消',
  }
  return textMap[status] || status
}

// 获取支付状态类型
function getPaymentStatusType(status) {
  const typeMap = {
    pending: 'warning',
    paid: 'success',
    refunded: 'error',
  }
  return typeMap[status] || 'default'
}

// 获取支付状态文本
function getPaymentStatusText(status) {
  const textMap = {
    pending: '待支付',
    paid: '已支付',
    refunded: '已退款',
  }
  return textMap[status] || status
}

// 查看订单详情
async function handleViewDetail(row) {
  try {
    const { data } = await api.getOrderDetail(row.id)
    orderDetail.value = data
    detailModalRef.value?.open({
      title: `订单详情 - ${row.orderNo}`,
      showFooter: false,
    })
  } catch (error) {
    $message.error('获取订单详情失败')
  }
}

// 表格列配置
const columns = [
  { title: '订单号', key: 'orderNo', width: 180, fixed: 'left' },
  {
    title: '用户信息',
    key: 'user',
    width: 150,
    render: row => h('div', [
      h('div', { class: 'font-medium' }, row.user?.name),
      h('div', { class: 'text-12 text-gray-500' }, row.user?.phone),
    ]),
  },
  { title: '商家名称', key: 'merchantName', width: 150 },
  {
    title: '订单状态',
    key: 'status',
    width: 100,
    render: row => h(NTag, {
      type: getStatusType(row.status),
      size: 'small',
    }, { default: () => getStatusText(row.status) }),
  },
  {
    title: '支付状态',
    key: 'paymentStatus',
    width: 100,
    render: row => h(NTag, {
      type: getPaymentStatusType(row.paymentStatus),
      size: 'small',
    }, { default: () => getPaymentStatusText(row.paymentStatus) }),
  },
  { title: '订单金额', key: 'totalAmount', width: 100, render: row => `¥${row.totalAmount}` },
  { title: '配送费', key: 'deliveryFee', width: 80, render: row => `¥${row.deliveryFee}` },
  { title: '配送员', key: 'deliveryUserName', width: 100 },
  { title: '下单时间', key: 'createdAt', width: 160, render: row => formatDateTime(row.createdAt) },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    fixed: 'right',
    render: row => [
      h(NButton, {
        size: 'small',
        type: 'primary',
        secondary: true,
        onClick: () => handleViewDetail(row),
      }, { default: () => '查看详情' }),
    ],
  },
]

// 刷新数据
function handleRefresh() {
  $table.value?.handleSearch()
  $message.success('数据已刷新')
}

// 导出数据
function handleExport() {
  $message.info('导出功能开发中...')
}
</script>
