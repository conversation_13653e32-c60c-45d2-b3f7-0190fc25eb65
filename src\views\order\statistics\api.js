/**********************************
 * @Description: 订单统计API
 * @Author: Campus Delivery Admin
 * @Date: 2024-01-01
 **********************************/

import { request } from '@/utils'

export default {
  // 获取订单统计
  getOrderStats: (params = {}) => request.get('/orders/stats', { params }),
  
  // 获取订单趋势数据
  getOrderTrends: (params = {}) => request.get('/orders/trends', { params }),
  
  // 获取日统计数据
  getDailyStats: (params = {}) => request.get('/orders/daily-stats', { params }),
  
  // 获取商家统计数据
  getMerchantStats: (params = {}) => request.get('/orders/merchant-stats', { params }),
  
  // 获取配送员统计数据
  getDeliveryStats: (params = {}) => request.get('/orders/delivery-stats', { params }),
  
  // 获取用户统计数据
  getUserStats: (params = {}) => request.get('/orders/user-stats', { params }),
  
  // 获取地区统计数据
  getRegionStats: (params = {}) => request.get('/orders/region-stats', { params }),
}
