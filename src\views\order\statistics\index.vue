<!--------------------------------
 - @Description: 订单统计分析
 - @Author: Campus Delivery Admin
 - @Date: 2024-01-01
 --------------------------------->

<template>
  <CommonPage show-footer>
    <template #action>
      <div class="flex-y-center justify-between">
        <h1 class="flex-y-center">
          <i class="i-material-symbols:analytics mr-8 text-20" />
          订单统计分析
        </h1>
        <div class="flex-y-center gap-12">
          <n-date-picker
            v-model:value="dateRange"
            type="daterange"
            clearable
            placeholder="选择时间范围"
            @update:value="handleDateChange"
          />
          <NButton type="primary" @click="handleRefresh">
            <i class="i-material-symbols:refresh mr-4 text-14" />
            刷新数据
          </NButton>
        </div>
      </div>
    </template>

    <div class="space-y-16">
      <!-- 统计卡片 -->
      <n-grid :cols="4" :x-gap="16">
        <n-grid-item>
          <n-card>
            <n-statistic label="今日订单" :value="stats.todayOrders">
              <template #prefix>
                <i class="i-material-symbols:receipt-long text-primary" />
              </template>
            </n-statistic>
          </n-card>
        </n-grid-item>
        <n-grid-item>
          <n-card>
            <n-statistic label="今日收入" :value="stats.todayIncome" :precision="2">
              <template #prefix>
                <span class="text-success">¥</span>
              </template>
            </n-statistic>
          </n-card>
        </n-grid-item>
        <n-grid-item>
          <n-card>
            <n-statistic label="完成率" :value="stats.completionRate" suffix="%">
              <template #prefix>
                <i class="i-material-symbols:check-circle text-success" />
              </template>
            </n-statistic>
          </n-card>
        </n-grid-item>
        <n-grid-item>
          <n-card>
            <n-statistic label="平均配送时间" :value="stats.avgDeliveryTime" suffix="分钟">
              <template #prefix>
                <i class="i-material-symbols:timer text-warning" />
              </template>
            </n-statistic>
          </n-card>
        </n-grid-item>
      </n-grid>

      <!-- 图表区域 -->
      <n-grid :cols="2" :x-gap="16">
        <!-- 订单趋势图 -->
        <n-grid-item>
          <n-card title="订单趋势">
            <div ref="orderTrendChart" class="h-300"></div>
          </n-card>
        </n-grid-item>

        <!-- 订单状态分布 -->
        <n-grid-item>
          <n-card title="订单状态分布">
            <div ref="statusPieChart" class="h-300"></div>
          </n-card>
        </n-grid-item>

        <!-- 热门商家排行 -->
        <n-grid-item>
          <n-card title="热门商家排行">
            <div ref="merchantRankChart" class="h-300"></div>
          </n-card>
        </n-grid-item>

        <!-- 配送时段分析 -->
        <n-grid-item>
          <n-card title="配送时段分析">
            <div ref="timeSlotChart" class="h-300"></div>
          </n-card>
        </n-grid-item>
      </n-grid>

      <!-- 详细数据表格 -->
      <n-card title="详细数据">
        <n-tabs type="line" animated>
          <n-tab-pane name="daily" tab="日统计">
            <n-table :bordered="false" :single-line="false">
              <thead>
                <tr>
                  <th>日期</th>
                  <th>订单数</th>
                  <th>完成数</th>
                  <th>取消数</th>
                  <th>完成率</th>
                  <th>收入金额</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="item in dailyStats" :key="item.date">
                  <td>{{ item.date }}</td>
                  <td>{{ item.totalOrders }}</td>
                  <td>{{ item.completedOrders }}</td>
                  <td>{{ item.cancelledOrders }}</td>
                  <td>{{ ((item.completedOrders / item.totalOrders) * 100).toFixed(1) }}%</td>
                  <td>¥{{ item.totalIncome }}</td>
                </tr>
              </tbody>
            </n-table>
          </n-tab-pane>

          <n-tab-pane name="merchant" tab="商家统计">
            <n-table :bordered="false" :single-line="false">
              <thead>
                <tr>
                  <th>商家名称</th>
                  <th>订单数</th>
                  <th>完成数</th>
                  <th>完成率</th>
                  <th>收入金额</th>
                  <th>平均评分</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="item in merchantStats" :key="item.merchantId">
                  <td>{{ item.merchantName }}</td>
                  <td>{{ item.totalOrders }}</td>
                  <td>{{ item.completedOrders }}</td>
                  <td>{{ ((item.completedOrders / item.totalOrders) * 100).toFixed(1) }}%</td>
                  <td>¥{{ item.totalIncome }}</td>
                  <td>{{ item.avgRating }}</td>
                </tr>
              </tbody>
            </n-table>
          </n-tab-pane>
        </n-tabs>
      </n-card>
    </div>
  </CommonPage>
</template>

<script setup>
import { NButton, NCard, NStatistic, NGrid, NGridItem, NTable, NTabs, NTabPane } from 'naive-ui'
import * as echarts from 'echarts'
import api from './api'

defineOptions({ name: 'OrderStatistics' })

const dateRange = ref([])
const stats = ref({
  todayOrders: 0,
  todayIncome: 0,
  completionRate: 0,
  avgDeliveryTime: 0,
})
const dailyStats = ref([])
const merchantStats = ref([])

// 图表引用
const orderTrendChart = ref(null)
const statusPieChart = ref(null)
const merchantRankChart = ref(null)
const timeSlotChart = ref(null)

// 图表实例
let orderTrendChartInstance = null
let statusPieChartInstance = null
let merchantRankChartInstance = null
let timeSlotChartInstance = null

onMounted(() => {
  initCharts()
  loadData()
})

onUnmounted(() => {
  // 销毁图表实例
  orderTrendChartInstance?.dispose()
  statusPieChartInstance?.dispose()
  merchantRankChartInstance?.dispose()
  timeSlotChartInstance?.dispose()
})

// 初始化图表
function initCharts() {
  nextTick(() => {
    orderTrendChartInstance = echarts.init(orderTrendChart.value)
    statusPieChartInstance = echarts.init(statusPieChart.value)
    merchantRankChartInstance = echarts.init(merchantRankChart.value)
    timeSlotChartInstance = echarts.init(timeSlotChart.value)
  })
}

// 加载数据
async function loadData() {
  try {
    const params = {}
    if (dateRange.value && dateRange.value.length === 2) {
      params.startDate = new Date(dateRange.value[0]).toISOString().split('T')[0]
      params.endDate = new Date(dateRange.value[1]).toISOString().split('T')[0]
    }

    // 加载统计数据
    const [statsRes, trendsRes, dailyRes, merchantRes] = await Promise.all([
      api.getOrderStats(params),
      api.getOrderTrends(params),
      api.getDailyStats(params),
      api.getMerchantStats(params),
    ])

    stats.value = statsRes.data
    dailyStats.value = dailyRes.data
    merchantStats.value = merchantRes.data

    // 更新图表
    updateOrderTrendChart(trendsRes.data.trends)
    updateStatusPieChart(trendsRes.data.statusDistribution)
    updateMerchantRankChart(trendsRes.data.merchantRank)
    updateTimeSlotChart(trendsRes.data.timeSlots)
  } catch (error) {
    $message.error('加载数据失败')
  }
}

// 更新订单趋势图
function updateOrderTrendChart(data) {
  const option = {
    title: { text: '订单趋势', left: 'center' },
    tooltip: { trigger: 'axis' },
    legend: { data: ['订单数', '完成数'], bottom: 0 },
    xAxis: { type: 'category', data: data.map(item => item.date) },
    yAxis: { type: 'value' },
    series: [
      {
        name: '订单数',
        type: 'line',
        data: data.map(item => item.totalOrders),
        smooth: true,
      },
      {
        name: '完成数',
        type: 'line',
        data: data.map(item => item.completedOrders),
        smooth: true,
      },
    ],
  }
  orderTrendChartInstance?.setOption(option)
}

// 更新状态分布饼图
function updateStatusPieChart(data) {
  const option = {
    title: { text: '订单状态分布', left: 'center' },
    tooltip: { trigger: 'item' },
    legend: { orient: 'vertical', left: 'left' },
    series: [
      {
        type: 'pie',
        radius: '50%',
        data: data,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
      },
    ],
  }
  statusPieChartInstance?.setOption(option)
}

// 更新商家排行图
function updateMerchantRankChart(data) {
  const option = {
    title: { text: '热门商家排行', left: 'center' },
    tooltip: { trigger: 'axis' },
    xAxis: { type: 'category', data: data.map(item => item.name) },
    yAxis: { type: 'value' },
    series: [
      {
        type: 'bar',
        data: data.map(item => item.orders),
        itemStyle: { color: '#FF6B35' },
      },
    ],
  }
  merchantRankChartInstance?.setOption(option)
}

// 更新时段分析图
function updateTimeSlotChart(data) {
  const option = {
    title: { text: '配送时段分析', left: 'center' },
    tooltip: { trigger: 'axis' },
    xAxis: { type: 'category', data: data.map(item => item.hour) },
    yAxis: { type: 'value' },
    series: [
      {
        type: 'bar',
        data: data.map(item => item.orders),
        itemStyle: { color: '#36CFC9' },
      },
    ],
  }
  timeSlotChartInstance?.setOption(option)
}

// 日期范围变化
function handleDateChange() {
  loadData()
}

// 刷新数据
function handleRefresh() {
  loadData()
  $message.success('数据已刷新')
}
</script>
