/**********************************
 * @Description: 商品分类管理API
 * @Author: Campus Delivery Admin
 * @Date: 2024-01-01
 **********************************/

import { request } from '@/utils'

export default {
  // 获取分类列表
  getCategories: (params = {}) => request.get('/product/categories', { params }),
  
  // 创建分类
  createCategory: data => request.post('/product/categories', data),
  
  // 更新分类
  updateCategory: data => request.patch(`/product/categories/${data.id}`, data),
  
  // 删除分类
  deleteCategory: id => request.delete(`/product/categories/${id}`),
  
  // 获取分类详情
  getCategoryDetail: id => request.get(`/product/categories/${id}`),
  
  // 更新分类状态
  updateCategoryStatus: (id, status) => request.patch(`/product/categories/${id}/status`, { status }),
  
  // 上传图片
  uploadImage: data => request.post('/upload/image', data),
  
  // 获取分类树形结构
  getCategoryTree: () => request.get('/product/categories/tree'),
}
