<!--------------------------------
 - @Description: 商品分类管理
 - @Author: Campus Delivery Admin
 - @Date: 2024-01-01
 --------------------------------->

<template>
  <CommonPage show-footer>
    <template #action>
      <div class="flex-y-center justify-between">
        <h1 class="flex-y-center">
          <i class="i-material-symbols:category mr-8 text-20" />
          商品分类管理
        </h1>
        <div class="flex-y-center gap-12">
          <NButton type="primary" @click="handleAdd">
            <i class="i-material-symbols:add mr-4 text-14" />
            新增分类
          </NButton>
          <NButton @click="handleRefresh">
            <i class="i-material-symbols:refresh mr-4 text-14" />
            刷新数据
          </NButton>
        </div>
      </div>
    </template>

    <MeCrud
      ref="$table"
      v-model:query-items="queryItems"
      :scroll-x="1200"
      :columns="columns"
      :get-data="api.getCategories"
    >
      <MeQueryItem label="分类名称" :label-width="70">
        <n-input
          v-model:value="queryItems.name"
          type="text"
          placeholder="请输入分类名称"
          clearable
        />
      </MeQueryItem>

      <MeQueryItem label="状态" :label-width="50">
        <n-select
          v-model:value="queryItems.status"
          clearable
          :options="[
            { label: '启用', value: 'active' },
            { label: '禁用', value: 'inactive' },
          ]"
        />
      </MeQueryItem>
    </MeCrud>

    <!-- 新增/编辑弹窗 -->
    <MeModal ref="modalRef" width="600px">
      <n-form
        ref="modalFormRef"
        label-placement="left"
        label-align="left"
        :label-width="100"
        :model="modalForm"
      >
        <n-form-item
          label="分类名称"
          path="name"
          :rule="{
            required: true,
            message: '请输入分类名称',
            trigger: ['input', 'blur'],
          }"
        >
          <n-input v-model:value="modalForm.name" placeholder="请输入分类名称" />
        </n-form-item>

        <n-form-item label="分类图标" path="icon">
          <div class="flex items-center gap-12">
            <n-input v-model:value="modalForm.icon" placeholder="请输入图标URL" />
            <n-upload
              :show-file-list="false"
              :custom-request="handleUploadIcon"
              accept="image/*"
            >
              <n-button size="small">上传图标</n-button>
            </n-upload>
          </div>
          <div v-if="modalForm.icon" class="mt-8">
            <n-image :src="modalForm.icon" width="60" height="60" object-fit="cover" />
          </div>
        </n-form-item>

        <n-form-item label="排序" path="sort">
          <n-input-number
            v-model:value="modalForm.sort"
            placeholder="排序值，数字越小越靠前"
            :min="0"
          />
        </n-form-item>

        <n-form-item label="状态" path="status">
          <n-radio-group v-model:value="modalForm.status">
            <n-radio value="active">启用</n-radio>
            <n-radio value="inactive">禁用</n-radio>
          </n-radio-group>
        </n-form-item>

        <n-form-item label="描述" path="description">
          <n-input
            v-model:value="modalForm.description"
            type="textarea"
            placeholder="请输入分类描述"
            :rows="3"
          />
        </n-form-item>
      </n-form>
    </MeModal>
  </CommonPage>
</template>

<script setup>
import { NButton, NTag, NImage, NUpload, NRadioGroup, NRadio, NInputNumber } from 'naive-ui'
import { MeCrud, MeModal, MeQueryItem } from '@/components'
import { useCrud } from '@/composables'
import { formatDateTime } from '@/utils'
import api from './api'

defineOptions({ name: 'ProductCategory' })

const $table = ref(null)
const queryItems = ref({})

onMounted(() => {
  $table.value?.handleSearch()
})

const { modalRef, modalFormRef, modalAction, modalForm, handleAdd, handleDelete, handleEdit }
  = useCrud({
    name: '分类',
    doCreate: api.createCategory,
    doDelete: api.deleteCategory,
    doUpdate: api.updateCategory,
    initForm: { status: 'active', sort: 0 },
    refresh: () => $table.value?.handleSearch(),
  })

// 上传图标
async function handleUploadIcon({ file }) {
  try {
    const formData = new FormData()
    formData.append('file', file.file)
    const { data } = await api.uploadImage(formData)
    modalForm.value.icon = data.url
    $message.success('图标上传成功')
  } catch (error) {
    $message.error('图标上传失败')
  }
}

// 切换状态
async function handleToggleStatus(row) {
  try {
    const newStatus = row.status === 'active' ? 'inactive' : 'active'
    await api.updateCategoryStatus(row.id, newStatus)
    $message.success('状态更新成功')
    $table.value?.handleSearch()
  } catch (error) {
    $message.error('状态更新失败')
  }
}

// 表格列配置
const columns = [
  {
    title: '分类信息',
    key: 'info',
    width: 200,
    fixed: 'left',
    render: row => h('div', { class: 'flex items-center gap-8' }, [
      h(NImage, {
        src: row.icon || '/default-category.png',
        width: 40,
        height: 40,
        objectFit: 'cover',
        class: 'rounded',
      }),
      h('div', [
        h('div', { class: 'font-medium' }, row.name),
        h('div', { class: 'text-12 text-gray-500' }, `ID: ${row.id}`),
      ]),
    ]),
  },
  { title: '描述', key: 'description', width: 200 },
  { title: '排序', key: 'sort', width: 80 },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render: row => h(NTag, {
      type: row.status === 'active' ? 'success' : 'error',
      size: 'small',
    }, { default: () => row.status === 'active' ? '启用' : '禁用' }),
  },
  { title: '商品数量', key: 'productCount', width: 100 },
  { title: '创建时间', key: 'createdAt', width: 160, render: row => formatDateTime(row.createdAt) },
  { title: '更新时间', key: 'updatedAt', width: 160, render: row => formatDateTime(row.updatedAt) },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    fixed: 'right',
    render: row => [
      h(NButton, {
        size: 'small',
        type: 'primary',
        secondary: true,
        onClick: () => handleEdit(row),
      }, { default: () => '编辑' }),
      h(NButton, {
        size: 'small',
        type: row.status === 'active' ? 'warning' : 'success',
        secondary: true,
        style: { marginLeft: '8px' },
        onClick: () => handleToggleStatus(row),
      }, { default: () => row.status === 'active' ? '禁用' : '启用' }),
      h(NButton, {
        size: 'small',
        type: 'error',
        secondary: true,
        style: { marginLeft: '8px' },
        onClick: () => handleDelete(row.id),
      }, { default: () => '删除' }),
    ],
  },
]

// 刷新数据
function handleRefresh() {
  $table.value?.handleSearch()
  $message.success('数据已刷新')
}
</script>
