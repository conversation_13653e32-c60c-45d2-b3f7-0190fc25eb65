/**********************************
 * @Description: 商品列表管理API
 * @Author: Campus Delivery Admin
 * @Date: 2024-01-01
 **********************************/

import { request } from '@/utils'

export default {
  // 获取商品列表
  getProducts: (params = {}) => request.get('/products', { params }),
  
  // 获取商品详情
  getProductDetail: id => request.get(`/products/${id}`),
  
  // 更新商品状态
  updateProductStatus: (id, data) => request.patch(`/products/${id}/status`, data),
  
  // 获取商品分类
  getCategories: () => request.get('/product/categories'),
  
  // 获取商品统计
  getProductStats: (params = {}) => request.get('/products/stats', { params }),
  
  // 批量操作商品
  batchUpdateProducts: data => request.patch('/products/batch', data),
  
  // 导出商品数据
  exportProducts: params => request.get('/products/export', { params, responseType: 'blob' }),
}
