<!--------------------------------
 - @Description: 商品列表管理
 - @Author: Campus Delivery Admin
 - @Date: 2024-01-01
 --------------------------------->

<template>
  <CommonPage show-footer>
    <template #action>
      <div class="flex-y-center justify-between">
        <h1 class="flex-y-center">
          <i class="i-material-symbols:inventory mr-8 text-20" />
          商品列表管理
        </h1>
        <div class="flex-y-center gap-12">
          <NButton @click="handleExport">
            <i class="i-material-symbols:download mr-4 text-14" />
            导出数据
          </NButton>
          <NButton type="primary" @click="handleRefresh">
            <i class="i-material-symbols:refresh mr-4 text-14" />
            刷新数据
          </NButton>
        </div>
      </div>
    </template>

    <MeCrud
      ref="$table"
      v-model:query-items="queryItems"
      :scroll-x="1800"
      :columns="columns"
      :get-data="api.getProducts"
    >
      <MeQueryItem label="商品名称" :label-width="70">
        <n-input
          v-model:value="queryItems.name"
          type="text"
          placeholder="请输入商品名称"
          clearable
        />
      </MeQueryItem>

      <MeQueryItem label="商家名称" :label-width="70">
        <n-input
          v-model:value="queryItems.merchantName"
          type="text"
          placeholder="请输入商家名称"
          clearable
        />
      </MeQueryItem>

      <MeQueryItem label="商品分类" :label-width="70">
        <n-select
          v-model:value="queryItems.categoryId"
          clearable
          placeholder="请选择商品分类"
          :options="categoryOptions"
          remote
          :loading="categoryLoading"
          @focus="loadCategories"
        />
      </MeQueryItem>

      <MeQueryItem label="商品状态" :label-width="70">
        <n-select
          v-model:value="queryItems.status"
          clearable
          :options="[
            { label: '上架', value: 'active' },
            { label: '下架', value: 'inactive' },
            { label: '售罄', value: 'sold_out' },
          ]"
        />
      </MeQueryItem>

      <MeQueryItem label="价格范围" :label-width="70">
        <div class="flex items-center gap-8">
          <n-input-number
            v-model:value="queryItems.minPrice"
            placeholder="最低价"
            :min="0"
            :precision="2"
          />
          <span>-</span>
          <n-input-number
            v-model:value="queryItems.maxPrice"
            placeholder="最高价"
            :min="0"
            :precision="2"
          />
        </div>
      </MeQueryItem>
    </MeCrud>

    <!-- 商品详情弹窗 -->
    <MeModal ref="detailModalRef" width="1000px" title="商品详情">
      <div v-if="productDetail" class="space-y-16">
        <!-- 基本信息 -->
        <n-card title="基本信息" size="small">
          <n-descriptions :column="3" bordered>
            <n-descriptions-item label="商品名称">{{ productDetail.name }}</n-descriptions-item>
            <n-descriptions-item label="商家名称">{{ productDetail.merchantName }}</n-descriptions-item>
            <n-descriptions-item label="商品分类">{{ productDetail.categoryName }}</n-descriptions-item>
            <n-descriptions-item label="商品价格">¥{{ productDetail.price }}</n-descriptions-item>
            <n-descriptions-item label="原价">¥{{ productDetail.originalPrice }}</n-descriptions-item>
            <n-descriptions-item label="库存数量">{{ productDetail.stock }}</n-descriptions-item>
            <n-descriptions-item label="销售数量">{{ productDetail.salesCount }}</n-descriptions-item>
            <n-descriptions-item label="商品状态">
              <NTag :type="getStatusType(productDetail.status)">
                {{ getStatusText(productDetail.status) }}
              </NTag>
            </n-descriptions-item>
            <n-descriptions-item label="评分">{{ productDetail.rating || 0 }}</n-descriptions-item>
          </n-descriptions>
        </n-card>

        <!-- 商品图片 -->
        <n-card title="商品图片" size="small">
          <n-grid :cols="4" :x-gap="16" :y-gap="16">
            <n-grid-item v-for="(image, index) in productDetail.images" :key="index">
              <n-image
                :src="image"
                width="150"
                height="150"
                object-fit="cover"
                class="rounded"
              />
            </n-grid-item>
          </n-grid>
        </n-card>

        <!-- 商品描述 -->
        <n-card title="商品描述" size="small">
          <div v-html="productDetail.description"></div>
        </n-card>

        <!-- 规格信息 -->
        <n-card v-if="productDetail.specifications?.length" title="规格信息" size="small">
          <n-table :bordered="false" :single-line="false">
            <thead>
              <tr>
                <th>规格名称</th>
                <th>价格</th>
                <th>库存</th>
                <th>状态</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="spec in productDetail.specifications" :key="spec.id">
                <td>{{ spec.name }}</td>
                <td>¥{{ spec.price }}</td>
                <td>{{ spec.stock }}</td>
                <td>
                  <NTag :type="spec.status === 'active' ? 'success' : 'error'" size="small">
                    {{ spec.status === 'active' ? '可用' : '不可用' }}
                  </NTag>
                </td>
              </tr>
            </tbody>
          </n-table>
        </n-card>

        <!-- 统计信息 -->
        <n-card title="统计信息" size="small">
          <n-grid :cols="4" :x-gap="16">
            <n-grid-item>
              <n-statistic label="浏览次数" :value="productDetail.stats?.viewCount || 0" />
            </n-grid-item>
            <n-grid-item>
              <n-statistic label="收藏次数" :value="productDetail.stats?.favoriteCount || 0" />
            </n-grid-item>
            <n-grid-item>
              <n-statistic label="评价数量" :value="productDetail.stats?.reviewCount || 0" />
            </n-grid-item>
            <n-grid-item>
              <n-statistic label="总销售额" :value="productDetail.stats?.totalSales || 0" :precision="2">
                <template #prefix>¥</template>
              </n-statistic>
            </n-grid-item>
          </n-grid>
        </n-card>
      </div>
    </MeModal>

    <!-- 状态操作弹窗 -->
    <MeModal ref="statusModalRef" width="500px" title="状态操作">
      <n-form
        ref="statusFormRef"
        label-placement="left"
        label-align="left"
        :label-width="100"
        :model="statusForm"
      >
        <n-form-item label="操作类型" path="action">
          <n-select
            v-model:value="statusForm.action"
            :options="statusActionOptions"
            placeholder="请选择操作类型"
          />
        </n-form-item>
        <n-form-item v-if="statusForm.action === 'inactive'" label="下架原因" path="reason">
          <n-input
            v-model:value="statusForm.reason"
            type="textarea"
            placeholder="请输入下架原因"
            :rows="3"
          />
        </n-form-item>
      </n-form>
    </MeModal>
  </CommonPage>
</template>

<script setup>
import { NButton, NTag, NCard, NDescriptions, NDescriptionsItem, NTable, NStatistic, NGrid, NGridItem, NImage, NInputNumber } from 'naive-ui'
import { MeCrud, MeModal, MeQueryItem } from '@/components'
import { formatDateTime } from '@/utils'
import api from './api'

defineOptions({ name: 'ProductList' })

const $table = ref(null)
const detailModalRef = ref(null)
const statusModalRef = ref(null)
const statusFormRef = ref(null)
const queryItems = ref({})
const productDetail = ref(null)
const categoryOptions = ref([])
const categoryLoading = ref(false)

const statusForm = ref({
  action: '',
  reason: '',
})

// 状态操作选项
const statusActionOptions = [
  { label: '上架商品', value: 'active' },
  { label: '下架商品', value: 'inactive' },
  { label: '标记售罄', value: 'sold_out' },
]

onMounted(() => {
  $table.value?.handleSearch()
  loadCategories()
})

// 加载分类选项
async function loadCategories() {
  if (categoryLoading.value) return
  categoryLoading.value = true
  try {
    const { data } = await api.getCategories()
    categoryOptions.value = data.map(item => ({
      label: item.name,
      value: item.id,
    }))
  } catch (error) {
    $message.error('加载分类失败')
  } finally {
    categoryLoading.value = false
  }
}

// 获取状态类型
function getStatusType(status) {
  const typeMap = {
    active: 'success',
    inactive: 'error',
    sold_out: 'warning',
  }
  return typeMap[status] || 'default'
}

// 获取状态文本
function getStatusText(status) {
  const textMap = {
    active: '上架',
    inactive: '下架',
    sold_out: '售罄',
  }
  return textMap[status] || status
}

// 查看商品详情
async function handleViewDetail(row) {
  try {
    const { data } = await api.getProductDetail(row.id)
    productDetail.value = data
    detailModalRef.value?.open({
      title: `商品详情 - ${row.name}`,
      showFooter: false,
    })
  } catch (error) {
    $message.error('获取商品详情失败')
  }
}

// 状态操作
function handleStatusAction(row) {
  statusForm.value = { action: '', reason: '' }
  statusModalRef.value?.open({
    title: `状态操作 - ${row.name}`,
    onOk: async () => {
      try {
        await api.updateProductStatus(row.id, statusForm.value)
        $message.success('操作成功')
        $table.value?.handleSearch()
        return true
      } catch (error) {
        $message.error('操作失败')
        return false
      }
    },
  })
}

// 表格列配置
const columns = [
  {
    title: '商品信息',
    key: 'info',
    width: 200,
    fixed: 'left',
    render: row => h('div', { class: 'flex items-center gap-8' }, [
      h(NImage, {
        src: row.image || '/default-product.png',
        width: 50,
        height: 50,
        objectFit: 'cover',
        class: 'rounded',
      }),
      h('div', [
        h('div', { class: 'font-medium' }, row.name),
        h('div', { class: 'text-12 text-gray-500' }, row.categoryName),
      ]),
    ]),
  },
  { title: '商家名称', key: 'merchantName', width: 150 },
  { title: '价格', key: 'price', width: 80, render: row => `¥${row.price}` },
  { title: '原价', key: 'originalPrice', width: 80, render: row => `¥${row.originalPrice}` },
  { title: '库存', key: 'stock', width: 80 },
  { title: '销量', key: 'salesCount', width: 80 },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render: row => h(NTag, {
      type: getStatusType(row.status),
      size: 'small',
    }, { default: () => getStatusText(row.status) }),
  },
  { title: '评分', key: 'rating', width: 80, render: row => row.rating || 0 },
  { title: '创建时间', key: 'createdAt', width: 160, render: row => formatDateTime(row.createdAt) },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    fixed: 'right',
    render: row => [
      h(NButton, {
        size: 'small',
        type: 'primary',
        secondary: true,
        onClick: () => handleViewDetail(row),
      }, { default: () => '查看详情' }),
      h(NButton, {
        size: 'small',
        type: 'warning',
        secondary: true,
        style: { marginLeft: '8px' },
        onClick: () => handleStatusAction(row),
      }, { default: () => '状态操作' }),
    ],
  },
]

// 刷新数据
function handleRefresh() {
  $table.value?.handleSearch()
  $message.success('数据已刷新')
}

// 导出数据
function handleExport() {
  $message.info('导出功能开发中...')
}
</script>
