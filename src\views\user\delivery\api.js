/**********************************
 * @Description: 配送员管理API
 * @Author: Campus Delivery Admin
 * @Date: 2024-01-01
 **********************************/

import { request } from '@/utils'

export default {
  // 获取配送员列表
  getDeliveryUsers: (params = {}) => request.get('/delivery-users', { params }),
  
  // 创建配送员
  createDeliveryUser: data => request.post('/delivery-users', data),
  
  // 更新配送员信息
  updateDeliveryUser: data => request.patch(`/delivery-users/${data.id}`, data),
  
  // 删除配送员
  deleteDeliveryUser: id => request.delete(`/delivery-users/${id}`),
  
  // 获取配送员详情
  getDeliveryUserDetail: id => request.get(`/delivery-users/${id}`),
  
  // 审核配送员
  auditDeliveryUser: (id, data) => request.patch(`/delivery-users/${id}/audit`, data),
  
  // 切换配送员工作状态
  toggleWorkStatus: (id, status) => request.patch(`/delivery-users/${id}/work-status`, { status }),
  
  // 获取配送员统计数据
  getDeliveryUserStats: id => request.get(`/delivery-users/${id}/stats`),
  
  // 获取配送员订单列表
  getDeliveryUserOrders: (id, params = {}) => request.get(`/delivery-users/${id}/orders`, { params }),
  
  // 获取配送员收入统计
  getDeliveryUserIncome: (id, params = {}) => request.get(`/delivery-users/${id}/income`, { params }),
  
  // 获取配送员位置信息
  getDeliveryUserLocation: id => request.get(`/delivery-users/${id}/location`),
  
  // 导出配送员数据
  exportDeliveryUsers: params => request.get('/delivery-users/export', { params, responseType: 'blob' }),
}
