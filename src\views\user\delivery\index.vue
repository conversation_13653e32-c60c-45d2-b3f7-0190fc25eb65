<!--------------------------------
 - @Description: 配送员管理
 - @Author: Campus Delivery Admin
 - @Date: 2024-01-01
 --------------------------------->

<template>
  <CommonPage show-footer>
    <template #action>
      <div class="flex-y-center justify-between">
        <h1 class="flex-y-center">
          <i class="i-material-symbols:delivery-truck mr-8 text-20" />
          配送员管理
        </h1>
        <div class="flex-y-center gap-12">
          <NButton type="primary" @click="handleAdd">
            <i class="i-material-symbols:add mr-4 text-14" />
            新增配送员
          </NButton>
          <NButton @click="handleExport">
            <i class="i-material-symbols:download mr-4 text-14" />
            导出数据
          </NButton>
        </div>
      </div>
    </template>

    <MeCrud
      ref="$table"
      v-model:query-items="queryItems"
      :scroll-x="1500"
      :columns="columns"
      :get-data="api.getDeliveryUsers"
    >
      <MeQueryItem label="姓名" :label-width="50">
        <n-input
          v-model:value="queryItems.name"
          type="text"
          placeholder="请输入姓名"
          clearable
        />
      </MeQueryItem>

      <MeQueryItem label="手机号" :label-width="50">
        <n-input
          v-model:value="queryItems.phone"
          type="text"
          placeholder="请输入手机号"
          clearable
        />
      </MeQueryItem>

      <MeQueryItem label="工作状态" :label-width="70">
        <n-select
          v-model:value="queryItems.workStatus"
          clearable
          :options="[
            { label: '在线', value: 'online' },
            { label: '离线', value: 'offline' },
            { label: '配送中', value: 'delivering' },
            { label: '休息中', value: 'resting' },
          ]"
        />
      </MeQueryItem>

      <MeQueryItem label="审核状态" :label-width="70">
        <n-select
          v-model:value="queryItems.auditStatus"
          clearable
          :options="[
            { label: '待审核', value: 'pending' },
            { label: '已通过', value: 'approved' },
            { label: '已拒绝', value: 'rejected' },
          ]"
        />
      </MeQueryItem>
    </MeCrud>

    <!-- 新增/编辑弹窗 -->
    <MeModal ref="modalRef" width="700px">
      <n-form
        ref="modalFormRef"
        label-placement="left"
        label-align="left"
        :label-width="100"
        :model="modalForm"
      >
        <n-grid :cols="2" :x-gap="24">
          <n-form-item-gi
            :span="1"
            label="姓名"
            path="name"
            :rule="{
              required: true,
              message: '请输入姓名',
              trigger: ['input', 'blur'],
            }"
          >
            <n-input v-model:value="modalForm.name" placeholder="请输入姓名" />
          </n-form-item-gi>

          <n-form-item-gi
            :span="1"
            label="手机号"
            path="phone"
            :rule="{
              required: true,
              message: '请输入手机号',
              trigger: ['input', 'blur'],
            }"
          >
            <n-input v-model:value="modalForm.phone" placeholder="请输入手机号" />
          </n-form-item-gi>

          <n-form-item-gi
            :span="1"
            label="身份证号"
            path="idCard"
            :rule="{
              required: true,
              message: '请输入身份证号',
              trigger: ['input', 'blur'],
            }"
          >
            <n-input v-model:value="modalForm.idCard" placeholder="请输入身份证号" />
          </n-form-item-gi>

          <n-form-item-gi
            :span="1"
            label="性别"
            path="gender"
            :rule="{
              required: true,
              message: '请选择性别',
              trigger: ['change', 'blur'],
            }"
          >
            <n-select
              v-model:value="modalForm.gender"
              placeholder="请选择性别"
              :options="[
                { label: '男', value: 'male' },
                { label: '女', value: 'female' },
              ]"
            />
          </n-form-item-gi>

          <n-form-item-gi :span="2" label="地址" path="address">
            <n-input v-model:value="modalForm.address" placeholder="请输入地址" />
          </n-form-item-gi>

          <n-form-item-gi
            :span="1"
            label="配送工具"
            path="vehicleType"
            :rule="{
              required: true,
              message: '请选择配送工具',
              trigger: ['change', 'blur'],
            }"
          >
            <n-select
              v-model:value="modalForm.vehicleType"
              placeholder="请选择配送工具"
              :options="[
                { label: '电动车', value: 'electric' },
                { label: '自行车', value: 'bicycle' },
                { label: '步行', value: 'walking' },
              ]"
            />
          </n-form-item-gi>

          <n-form-item-gi :span="1" label="车牌号" path="vehicleNumber">
            <n-input v-model:value="modalForm.vehicleNumber" placeholder="请输入车牌号" />
          </n-form-item-gi>

          <n-form-item-gi :span="1" label="紧急联系人" path="emergencyContact">
            <n-input v-model:value="modalForm.emergencyContact" placeholder="请输入紧急联系人" />
          </n-form-item-gi>

          <n-form-item-gi :span="1" label="紧急联系电话" path="emergencyPhone">
            <n-input v-model:value="modalForm.emergencyPhone" placeholder="请输入紧急联系电话" />
          </n-form-item-gi>

          <n-form-item-gi :span="1" label="工作状态" path="workStatus">
            <n-select
              v-model:value="modalForm.workStatus"
              :options="[
                { label: '在线', value: 'online' },
                { label: '离线', value: 'offline' },
                { label: '休息中', value: 'resting' },
              ]"
            />
          </n-form-item-gi>

          <n-form-item-gi :span="1" label="审核状态" path="auditStatus">
            <n-select
              v-model:value="modalForm.auditStatus"
              :options="[
                { label: '待审核', value: 'pending' },
                { label: '已通过', value: 'approved' },
                { label: '已拒绝', value: 'rejected' },
              ]"
            />
          </n-form-item-gi>
        </n-grid>
      </n-form>
    </MeModal>
  </CommonPage>
</template>

<script setup>
import { NButton, NSwitch, NTag, NAvatar, NRate } from 'naive-ui'
import { MeCrud, MeModal, MeQueryItem } from '@/components'
import { useCrud } from '@/composables'
import { formatDateTime } from '@/utils'
import api from './api'

defineOptions({ name: 'DeliveryUsers' })

const $table = ref(null)
const queryItems = ref({})

onMounted(() => {
  $table.value?.handleSearch()
})

const { modalRef, modalFormRef, modalAction, modalForm, handleAdd, handleDelete, handleEdit }
  = useCrud({
    name: '配送员',
    doCreate: api.createDeliveryUser,
    doDelete: api.deleteDeliveryUser,
    doUpdate: api.updateDeliveryUser,
    initForm: { workStatus: 'offline', auditStatus: 'pending' },
    refresh: () => $table.value?.handleSearch(),
  })

// 表格列配置
const columns = [
  {
    title: '配送员信息',
    key: 'info',
    width: 180,
    fixed: 'left',
    render: row => h('div', { class: 'flex items-center gap-8' }, [
      h(NAvatar, {
        size: 'small',
        src: row.avatar,
        fallbackSrc: '/default-avatar.png',
      }),
      h('div', [
        h('div', { class: 'font-medium' }, row.name),
        h('div', { class: 'text-12 text-gray-500' }, row.phone),
      ]),
    ]),
  },
  { title: '身份证号', key: 'idCard', width: 160 },
  { title: '配送工具', key: 'vehicleType', width: 100 },
  { title: '车牌号', key: 'vehicleNumber', width: 120 },
  {
    title: '工作状态',
    key: 'workStatus',
    width: 100,
    render: row => {
      const statusMap = {
        online: { type: 'success', text: '在线' },
        offline: { type: 'default', text: '离线' },
        delivering: { type: 'info', text: '配送中' },
        resting: { type: 'warning', text: '休息中' },
      }
      const status = statusMap[row.workStatus] || statusMap.offline
      return h(NTag, { type: status.type, size: 'small' }, { default: () => status.text })
    },
  },
  {
    title: '审核状态',
    key: 'auditStatus',
    width: 100,
    render: row => {
      const statusMap = {
        pending: { type: 'warning', text: '待审核' },
        approved: { type: 'success', text: '已通过' },
        rejected: { type: 'error', text: '已拒绝' },
      }
      const status = statusMap[row.auditStatus] || statusMap.pending
      return h(NTag, { type: status.type, size: 'small' }, { default: () => status.text })
    },
  },
  {
    title: '评分',
    key: 'rating',
    width: 120,
    render: row => h(NRate, {
      readonly: true,
      size: 'small',
      value: row.rating || 0,
    }),
  },
  { title: '完成订单', key: 'completedOrders', width: 100 },
  { title: '入职时间', key: 'createdAt', width: 160, render: row => formatDateTime(row.createdAt) },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    fixed: 'right',
    render: row => [
      h(NButton, {
        size: 'small',
        type: 'primary',
        secondary: true,
        onClick: () => handleEdit(row),
      }, { default: () => '编辑' }),
      h(NButton, {
        size: 'small',
        type: 'error',
        secondary: true,
        style: { marginLeft: '8px' },
        onClick: () => handleDelete(row.id),
      }, { default: () => '删除' }),
    ],
  },
]

// 导出数据
function handleExport() {
  $message.info('导出功能开发中...')
}
</script>
