/**********************************
 * @Description: 商家用户管理API
 * @Author: Campus Delivery Admin
 * @Date: 2024-01-01
 **********************************/

import { request } from '@/utils'

export default {
  // 获取商家列表
  getMerchants: (params = {}) => request.get('/merchants', { params }),
  
  // 创建商家
  createMerchant: data => request.post('/merchants', data),
  
  // 更新商家信息
  updateMerchant: data => request.patch(`/merchants/${data.id}`, data),
  
  // 删除商家
  deleteMerchant: id => request.delete(`/merchants/${id}`),
  
  // 获取商家详情
  getMerchantDetail: id => request.get(`/merchants/${id}`),
  
  // 审核商家
  auditMerchant: (id, data) => request.patch(`/merchants/${id}/audit`, data),
  
  // 切换商家营业状态
  toggleBusinessStatus: (id, status) => request.patch(`/merchants/${id}/business-status`, { status }),
  
  // 获取商家统计数据
  getMerchantStats: id => request.get(`/merchants/${id}/stats`),
  
  // 获取商家订单列表
  getMerchantOrders: (id, params = {}) => request.get(`/merchants/${id}/orders`, { params }),
  
  // 获取商家商品列表
  getMerchantProducts: (id, params = {}) => request.get(`/merchants/${id}/products`, { params }),
  
  // 获取商家收入统计
  getMerchantIncome: (id, params = {}) => request.get(`/merchants/${id}/income`, { params }),
  
  // 导出商家数据
  exportMerchants: params => request.get('/merchants/export', { params, responseType: 'blob' }),
}
