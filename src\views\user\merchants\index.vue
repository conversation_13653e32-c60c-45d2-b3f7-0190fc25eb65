<!--------------------------------
 - @Description: 商家用户管理
 - @Author: Campus Delivery Admin
 - @Date: 2024-01-01
 --------------------------------->

<template>
  <CommonPage show-footer>
    <template #action>
      <div class="flex-y-center justify-between">
        <h1 class="flex-y-center">
          <i class="i-material-symbols:store mr-8 text-20" />
          商家用户管理
        </h1>
        <div class="flex-y-center gap-12">
          <NButton type="primary" @click="handleAdd">
            <i class="i-material-symbols:add mr-4 text-14" />
            新增商家
          </NButton>
          <NButton @click="handleExport">
            <i class="i-material-symbols:download mr-4 text-14" />
            导出数据
          </NButton>
        </div>
      </div>
    </template>

    <MeCrud
      ref="$table"
      v-model:query-items="queryItems"
      :scroll-x="1600"
      :columns="columns"
      :get-data="api.getMerchants"
    >
      <MeQueryItem label="商家名称" :label-width="70">
        <n-input
          v-model:value="queryItems.name"
          type="text"
          placeholder="请输入商家名称"
          clearable
        />
      </MeQueryItem>

      <MeQueryItem label="联系人" :label-width="70">
        <n-input
          v-model:value="queryItems.contactName"
          type="text"
          placeholder="请输入联系人"
          clearable
        />
      </MeQueryItem>

      <MeQueryItem label="商家类型" :label-width="70">
        <n-select
          v-model:value="queryItems.category"
          clearable
          placeholder="请选择商家类型"
          :options="categoryOptions"
        />
      </MeQueryItem>

      <MeQueryItem label="营业状态" :label-width="70">
        <n-select
          v-model:value="queryItems.businessStatus"
          clearable
          :options="[
            { label: '营业中', value: 'open' },
            { label: '休息中', value: 'closed' },
            { label: '暂停营业', value: 'suspended' },
          ]"
        />
      </MeQueryItem>

      <MeQueryItem label="审核状态" :label-width="70">
        <n-select
          v-model:value="queryItems.auditStatus"
          clearable
          :options="[
            { label: '待审核', value: 'pending' },
            { label: '已通过', value: 'approved' },
            { label: '已拒绝', value: 'rejected' },
          ]"
        />
      </MeQueryItem>
    </MeCrud>

    <!-- 新增/编辑弹窗 -->
    <MeModal ref="modalRef" width="800px">
      <n-form
        ref="modalFormRef"
        label-placement="left"
        label-align="left"
        :label-width="100"
        :model="modalForm"
      >
        <n-grid :cols="2" :x-gap="24">
          <n-form-item-gi
            :span="1"
            label="商家名称"
            path="name"
            :rule="{
              required: true,
              message: '请输入商家名称',
              trigger: ['input', 'blur'],
            }"
          >
            <n-input v-model:value="modalForm.name" placeholder="请输入商家名称" />
          </n-form-item-gi>

          <n-form-item-gi
            :span="1"
            label="商家类型"
            path="category"
            :rule="{
              required: true,
              message: '请选择商家类型',
              trigger: ['change', 'blur'],
            }"
          >
            <n-select
              v-model:value="modalForm.category"
              placeholder="请选择商家类型"
              :options="categoryOptions"
            />
          </n-form-item-gi>

          <n-form-item-gi
            :span="1"
            label="联系人"
            path="contactName"
            :rule="{
              required: true,
              message: '请输入联系人',
              trigger: ['input', 'blur'],
            }"
          >
            <n-input v-model:value="modalForm.contactName" placeholder="请输入联系人" />
          </n-form-item-gi>

          <n-form-item-gi
            :span="1"
            label="联系电话"
            path="contactPhone"
            :rule="{
              required: true,
              message: '请输入联系电话',
              trigger: ['input', 'blur'],
            }"
          >
            <n-input v-model:value="modalForm.contactPhone" placeholder="请输入联系电话" />
          </n-form-item-gi>

          <n-form-item-gi :span="2" label="商家地址" path="address">
            <n-input v-model:value="modalForm.address" placeholder="请输入商家地址" />
          </n-form-item-gi>

          <n-form-item-gi :span="2" label="商家描述" path="description">
            <n-input
              v-model:value="modalForm.description"
              type="textarea"
              placeholder="请输入商家描述"
              :rows="3"
            />
          </n-form-item-gi>

          <n-form-item-gi :span="1" label="营业时间" path="businessHours">
            <n-input v-model:value="modalForm.businessHours" placeholder="如：09:00-22:00" />
          </n-form-item-gi>

          <n-form-item-gi :span="1" label="配送费" path="deliveryFee">
            <n-input-number
              v-model:value="modalForm.deliveryFee"
              placeholder="配送费"
              :min="0"
              :precision="2"
            />
          </n-form-item-gi>

          <n-form-item-gi :span="1" label="起送金额" path="minOrderAmount">
            <n-input-number
              v-model:value="modalForm.minOrderAmount"
              placeholder="起送金额"
              :min="0"
              :precision="2"
            />
          </n-form-item-gi>

          <n-form-item-gi :span="1" label="营业状态" path="businessStatus">
            <n-select
              v-model:value="modalForm.businessStatus"
              :options="[
                { label: '营业中', value: 'open' },
                { label: '休息中', value: 'closed' },
                { label: '暂停营业', value: 'suspended' },
              ]"
            />
          </n-form-item-gi>
        </n-grid>
      </n-form>
    </MeModal>
  </CommonPage>
</template>

<script setup>
import { NButton, NSwitch, NTag, NAvatar, NRate } from 'naive-ui'
import { MeCrud, MeModal, MeQueryItem } from '@/components'
import { useCrud } from '@/composables'
import { formatDateTime } from '@/utils'
import api from './api'

defineOptions({ name: 'MerchantUsers' })

const $table = ref(null)
const queryItems = ref({})

// 商家类型选项
const categoryOptions = [
  { label: '餐饮美食', value: 'food' },
  { label: '生活超市', value: 'supermarket' },
  { label: '水果生鲜', value: 'fruit' },
  { label: '饮品奶茶', value: 'drink' },
  { label: '零食小食', value: 'snack' },
  { label: '其他', value: 'other' },
]

onMounted(() => {
  $table.value?.handleSearch()
})

const { modalRef, modalFormRef, modalAction, modalForm, handleAdd, handleDelete, handleEdit }
  = useCrud({
    name: '商家',
    doCreate: api.createMerchant,
    doDelete: api.deleteMerchant,
    doUpdate: api.updateMerchant,
    initForm: { businessStatus: 'open', deliveryFee: 3, minOrderAmount: 20 },
    refresh: () => $table.value?.handleSearch(),
  })

// 表格列配置
const columns = [
  {
    title: '商家信息',
    key: 'info',
    width: 200,
    fixed: 'left',
    render: row => h('div', { class: 'flex items-center gap-8' }, [
      h(NAvatar, {
        size: 'small',
        src: row.logo,
        fallbackSrc: '/default-store.png',
      }),
      h('div', [
        h('div', { class: 'font-medium' }, row.name),
        h('div', { class: 'text-12 text-gray-500' }, row.category),
      ]),
    ]),
  },
  { title: '联系人', key: 'contactName', width: 100 },
  { title: '联系电话', key: 'contactPhone', width: 120 },
  {
    title: '评分',
    key: 'rating',
    width: 120,
    render: row => h(NRate, {
      readonly: true,
      size: 'small',
      value: row.rating || 0,
    }),
  },
  {
    title: '营业状态',
    key: 'businessStatus',
    width: 100,
    render: row => {
      const statusMap = {
        open: { type: 'success', text: '营业中' },
        closed: { type: 'warning', text: '休息中' },
        suspended: { type: 'error', text: '暂停营业' },
      }
      const status = statusMap[row.businessStatus] || statusMap.open
      return h(NTag, { type: status.type, size: 'small' }, { default: () => status.text })
    },
  },
  {
    title: '审核状态',
    key: 'auditStatus',
    width: 100,
    render: row => {
      const statusMap = {
        pending: { type: 'warning', text: '待审核' },
        approved: { type: 'success', text: '已通过' },
        rejected: { type: 'error', text: '已拒绝' },
      }
      const status = statusMap[row.auditStatus] || statusMap.pending
      return h(NTag, { type: status.type, size: 'small' }, { default: () => status.text })
    },
  },
  { title: '配送费', key: 'deliveryFee', width: 80, render: row => `¥${row.deliveryFee}` },
  { title: '起送金额', key: 'minOrderAmount', width: 100, render: row => `¥${row.minOrderAmount}` },
  { title: '入驻时间', key: 'createdAt', width: 160, render: row => formatDateTime(row.createdAt) },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    fixed: 'right',
    render: row => [
      h(NButton, {
        size: 'small',
        type: 'primary',
        secondary: true,
        onClick: () => handleEdit(row),
      }, { default: () => '编辑' }),
      h(NButton, {
        size: 'small',
        type: 'error',
        secondary: true,
        style: { marginLeft: '8px' },
        onClick: () => handleDelete(row.id),
      }, { default: () => '删除' }),
    ],
  },
]

// 导出数据
function handleExport() {
  $message.info('导出功能开发中...')
}
</script>
