/**********************************
 * @Description: 学生用户管理API
 * @Author: Campus Delivery Admin
 * @Date: 2024-01-01
 **********************************/

import { request } from '@/utils'

export default {
  // 获取学生列表
  getStudents: (params = {}) => request.get('/students', { params }),
  
  // 创建学生
  createStudent: data => request.post('/students', data),
  
  // 更新学生信息
  updateStudent: data => request.patch(`/students/${data.id}`, data),
  
  // 删除学生
  deleteStudent: id => request.delete(`/students/${id}`),
  
  // 获取学生详情
  getStudentDetail: id => request.get(`/students/${id}`),
  
  // 冻结/解冻学生账户
  toggleStudentStatus: (id, status) => request.patch(`/students/${id}/status`, { status }),
  
  // 重置学生密码
  resetStudentPassword: id => request.patch(`/students/${id}/reset-password`),
  
  // 获取学生订单统计
  getStudentOrderStats: id => request.get(`/students/${id}/order-stats`),
  
  // 获取学生消费记录
  getStudentConsumption: (id, params = {}) => request.get(`/students/${id}/consumption`, { params }),
  
  // 导出学生数据
  exportStudents: params => request.get('/students/export', { params, responseType: 'blob' }),
}
