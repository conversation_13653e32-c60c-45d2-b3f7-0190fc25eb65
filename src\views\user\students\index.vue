<!--------------------------------
 - @Description: 学生用户管理
 - @Author: Campus Delivery Admin
 - @Date: 2024-01-01
 --------------------------------->

<template>
  <CommonPage show-footer>
    <template #action>
      <div class="flex-y-center justify-between">
        <h1 class="flex-y-center">
          <i class="i-material-symbols:school mr-8 text-20" />
          学生用户管理
        </h1>
        <div class="flex-y-center gap-12">
          <NButton type="primary" @click="handleAdd">
            <i class="i-material-symbols:add mr-4 text-14" />
            新增学生
          </NButton>
          <NButton @click="handleExport">
            <i class="i-material-symbols:download mr-4 text-14" />
            导出数据
          </NButton>
        </div>
      </div>
    </template>

    <MeCrud
      ref="$table"
      v-model:query-items="queryItems"
      :scroll-x="1400"
      :columns="columns"
      :get-data="api.getStudents"
    >
      <MeQueryItem label="学号" :label-width="50">
        <n-input
          v-model:value="queryItems.studentId"
          type="text"
          placeholder="请输入学号"
          clearable
        />
      </MeQueryItem>

      <MeQueryItem label="姓名" :label-width="50">
        <n-input
          v-model:value="queryItems.name"
          type="text"
          placeholder="请输入姓名"
          clearable
        />
      </MeQueryItem>

      <MeQueryItem label="学院" :label-width="50">
        <n-select
          v-model:value="queryItems.college"
          clearable
          placeholder="请选择学院"
          :options="collegeOptions"
        />
      </MeQueryItem>

      <MeQueryItem label="状态" :label-width="50">
        <n-select
          v-model:value="queryItems.status"
          clearable
          :options="[
            { label: '正常', value: 'active' },
            { label: '冻结', value: 'frozen' },
            { label: '注销', value: 'deleted' },
          ]"
        />
      </MeQueryItem>
    </MeCrud>

    <!-- 新增/编辑弹窗 -->
    <MeModal ref="modalRef" width="600px">
      <n-form
        ref="modalFormRef"
        label-placement="left"
        label-align="left"
        :label-width="80"
        :model="modalForm"
      >
        <n-form-item
          label="学号"
          path="studentId"
          :rule="{
            required: true,
            message: '请输入学号',
            trigger: ['input', 'blur'],
          }"
        >
          <n-input v-model:value="modalForm.studentId" placeholder="请输入学号" />
        </n-form-item>

        <n-form-item
          label="姓名"
          path="name"
          :rule="{
            required: true,
            message: '请输入姓名',
            trigger: ['input', 'blur'],
          }"
        >
          <n-input v-model:value="modalForm.name" placeholder="请输入姓名" />
        </n-form-item>

        <n-form-item
          label="手机号"
          path="phone"
          :rule="{
            required: true,
            message: '请输入手机号',
            trigger: ['input', 'blur'],
          }"
        >
          <n-input v-model:value="modalForm.phone" placeholder="请输入手机号" />
        </n-form-item>

        <n-form-item
          label="学院"
          path="college"
          :rule="{
            required: true,
            message: '请选择学院',
            trigger: ['change', 'blur'],
          }"
        >
          <n-select
            v-model:value="modalForm.college"
            placeholder="请选择学院"
            :options="collegeOptions"
          />
        </n-form-item>

        <n-form-item
          label="专业"
          path="major"
          :rule="{
            required: true,
            message: '请输入专业',
            trigger: ['input', 'blur'],
          }"
        >
          <n-input v-model:value="modalForm.major" placeholder="请输入专业" />
        </n-form-item>

        <n-form-item
          label="年级"
          path="grade"
          :rule="{
            required: true,
            message: '请选择年级',
            trigger: ['change', 'blur'],
          }"
        >
          <n-select
            v-model:value="modalForm.grade"
            placeholder="请选择年级"
            :options="gradeOptions"
          />
        </n-form-item>

        <n-form-item label="宿舍地址" path="dormitory">
          <n-input v-model:value="modalForm.dormitory" placeholder="请输入宿舍地址" />
        </n-form-item>

        <n-form-item label="状态" path="status">
          <n-select
            v-model:value="modalForm.status"
            :options="[
              { label: '正常', value: 'active' },
              { label: '冻结', value: 'frozen' },
            ]"
          />
        </n-form-item>
      </n-form>
    </MeModal>
  </CommonPage>
</template>

<script setup>
import { NButton, NSwitch, NTag, NAvatar } from 'naive-ui'
import { MeCrud, MeModal, MeQueryItem } from '@/components'
import { useCrud } from '@/composables'
import { formatDateTime } from '@/utils'
import api from './api'

defineOptions({ name: 'StudentUsers' })

const $table = ref(null)
const queryItems = ref({})

// 学院选项
const collegeOptions = [
  { label: '计算机学院', value: 'computer' },
  { label: '电子信息学院', value: 'electronic' },
  { label: '机械工程学院', value: 'mechanical' },
  { label: '经济管理学院', value: 'economics' },
  { label: '外国语学院', value: 'foreign' },
  { label: '艺术学院', value: 'art' },
]

// 年级选项
const gradeOptions = [
  { label: '2024级', value: '2024' },
  { label: '2023级', value: '2023' },
  { label: '2022级', value: '2022' },
  { label: '2021级', value: '2021' },
]

onMounted(() => {
  $table.value?.handleSearch()
})

const { modalRef, modalFormRef, modalAction, modalForm, handleAdd, handleDelete, handleEdit }
  = useCrud({
    name: '学生',
    doCreate: api.createStudent,
    doDelete: api.deleteStudent,
    doUpdate: api.updateStudent,
    initForm: { status: 'active' },
    refresh: () => $table.value?.handleSearch(),
  })

// 表格列配置
const columns = [
  { title: '学号', key: 'studentId', width: 120, fixed: 'left' },
  {
    title: '头像',
    key: 'avatar',
    width: 80,
    render: row => h(NAvatar, {
      size: 'small',
      src: row.avatar,
      fallbackSrc: '/default-avatar.png',
    }),
  },
  { title: '姓名', key: 'name', width: 100 },
  { title: '手机号', key: 'phone', width: 120 },
  { title: '学院', key: 'collegeName', width: 120 },
  { title: '专业', key: 'major', width: 120 },
  { title: '年级', key: 'grade', width: 80 },
  { title: '宿舍', key: 'dormitory', width: 120 },
  {
    title: '状态',
    key: 'status',
    width: 80,
    render: row => {
      const statusMap = {
        active: { type: 'success', text: '正常' },
        frozen: { type: 'warning', text: '冻结' },
        deleted: { type: 'error', text: '注销' },
      }
      const status = statusMap[row.status] || statusMap.active
      return h(NTag, { type: status.type, size: 'small' }, { default: () => status.text })
    },
  },
  { title: '注册时间', key: 'createdAt', width: 160, render: row => formatDateTime(row.createdAt) },
  { title: '最后登录', key: 'lastLoginAt', width: 160, render: row => formatDateTime(row.lastLoginAt) },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    fixed: 'right',
    render: row => [
      h(NButton, {
        size: 'small',
        type: 'primary',
        secondary: true,
        onClick: () => handleEdit(row),
      }, { default: () => '编辑' }),
      h(NButton, {
        size: 'small',
        type: 'error',
        secondary: true,
        style: { marginLeft: '8px' },
        onClick: () => handleDelete(row.id),
      }, { default: () => '删除' }),
    ],
  },
]

// 导出数据
function handleExport() {
  $message.info('导出功能开发中...')
}
</script>
